//
// Created by <PERSON><PERSON><PERSON> on 2022/7/16.
//
#ifndef LEGGEDINTERFACE_H
#define LEGGEDINTERFACE_H


#include <ocs2_centroidal_model/CentroidalModelInfo.h>
#include <ocs2_core/Types.h>
#include <ocs2_core/initialization/Initializer.h>
#include <ocs2_core/penalties/Penalties.h>
#include <ocs2_legged_robot/common/ModelSettings.h>
#include <ocs2_mpc/MPC_Settings.h>
#include <ocs2_oc/rollout/TimeTriggeredRollout.h>
#include <ocs2_pinocchio_interface/PinocchioInterface.h>
#include <ocs2_robotic_tools/common/RobotInterface.h>
#include <ocs2_robotic_tools/end_effector/EndEffectorKinematics.h>
#include <ocs2_self_collision/PinocchioGeometryInterface.h>
#include <ocs2_sqp/SqpSettings.h>

#include "SwitchedModelReferenceManager.h"

namespace ocs2::legged_robot
{
    class LeggedInterface : public RobotInterface
    {
    public:
        LeggedInterface(const std::string& task_file,
                        const std::string& urdf_file,
                        const std::string& reference_file,
                        bool use_hard_friction_cone_constraint = false);

        ~LeggedInterface() override = default;

        void setupJointNames(const std::vector<std::string>& joint_names,
                             const std::vector<std::string>& foot_names);

        virtual void setupOptimalControlProblem(const std::string& task_file,
                                                const std::string& urdf_file,
                                                const std::string& reference_file,
                                                bool verbose);
        // === 核心接口功能 ===
        const OptimalControlProblem& getOptimalControlProblem() const override { return *problem_ptr_; }//获取最优控制问题

        const ModelSettings& modelSettings() const { return model_settings_; } //获取模型设置
        const mpc::Settings& mpcSettings() const { return mpc_settings_; } //获取MPC设置
        const sqp::Settings& sqpSettings() { return sqp_settings_; } //获取SQP设置

        const RolloutBase& getRollout() const { return *rollout_ptr_; }

        // === 机器人模型接口 ===
        PinocchioInterface& getPinocchioInterface() { return *pinocchio_interface_ptr_; }
        const CentroidalModelInfo& getCentroidalModelInfo() const { return centroidal_model_info_; }
        // === 参考管理 ===
        std::shared_ptr<SwitchedModelReferenceManager> getSwitchedModelReferenceManagerPtr() const
        {
            return reference_manager_ptr_;
        }
        // === 初始化器 ===
        const Initializer& getInitializer() const override { return *initializer_ptr_; } //轨迹初始化器

        std::shared_ptr<ReferenceManagerInterface> getReferenceManagerPtr() const override
        {
            return reference_manager_ptr_;
        }

    protected:
        void setupModel(const std::string& task_file, const std::string& urdf_file,
                        const std::string& reference_file);

        virtual void setupReferenceManager(const std::string& taskFile, const std::string& urdfFile,
                                           const std::string& referenceFile,
                                           bool verbose);

        std::shared_ptr<GaitSchedule> loadGaitSchedule(const std::string& file, bool verbose) const;

        std::unique_ptr<StateInputCost> getBaseTrackingCost(const std::string& taskFile,
                                                            const CentroidalModelInfo& info, bool verbose);

        matrix_t initializeInputCostWeight(const std::string& taskFile, const CentroidalModelInfo& info);

        static std::pair<scalar_t, RelaxedBarrierPenalty::Config> loadFrictionConeSettings(
            const std::string& taskFile, bool verbose);

        std::unique_ptr<StateInputConstraint> getFrictionConeConstraint(size_t contactPointIndex,
                                                                        scalar_t frictionCoefficient);

        std::unique_ptr<StateInputCost> getFrictionConeSoftConstraint(size_t contactPointIndex,
                                                                      scalar_t frictionCoefficient,
                                                                      const RelaxedBarrierPenalty::Config&
                                                                      barrierPenaltyConfig);

        std::unique_ptr<EndEffectorKinematics<scalar_t>> getEeKinematicsPtr(const std::vector<std::string>& foot_names,
                                                                            const std::string& model_name);

        std::unique_ptr<StateInputConstraint> getZeroVelocityConstraint(
            const EndEffectorKinematics<scalar_t>& end_effector_kinematics,
            size_t contact_point_index);

        std::unique_ptr<StateCost> getSelfCollisionConstraint(const PinocchioInterface& pinocchioInterface,
                                                              const std::string& taskFile,
                                                              const std::string& prefix, bool verbose);

        ModelSettings model_settings_; // 模型设置
        mpc::Settings mpc_settings_; // MPC设置
        sqp::Settings sqp_settings_; // SQP设置
        const bool use_hard_friction_cone_constraint_;

        std::unique_ptr<PinocchioInterface> pinocchio_interface_ptr_;  //动力学接口
        CentroidalModelInfo centroidal_model_info_; //质心模型信息
        std::unique_ptr<PinocchioGeometryInterface> geometry_interface_ptr_;

        std::unique_ptr<OptimalControlProblem> problem_ptr_; // 优化控制问题
        std::shared_ptr<SwitchedModelReferenceManager> reference_manager_ptr_;

        rollout::Settings rollout_settings_; 
        std::unique_ptr<RolloutBase> rollout_ptr_; // 轨迹积分器
        std::unique_ptr<Initializer> initializer_ptr_;   //初始化器

        vector_t initial_state_;
    };
} // namespace legged
#endif // LEGGEDINTERFACE_H
