<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>mit_controller</name>
  <version>0.0.0</version>
  <description>A Ros2 Control Controller based on MIT Controller</description>
  <maintainer email="<EMAIL>">hjy</maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <build_depend>generate_parameter_library</build_depend>

  <depend>backward_ros</depend>
  <depend>control_input_msgs</depend>
  <depend>controller_interface</depend>
  <!-- <depend>hardware_interface</depend> -->
  <depend>pluginlib</depend>
  <depend>rclcpp</depend>
  <!--基本的 URDF 解析和运动学计算 库-->
  <depend>kdl_parser</depend>

  <exec_depend>controller_manager</exec_depend>
  <exec_depend>joint_state_broadcaster</exec_depend>

  <!-- <depend>rclcpp_lifecycle</depend>
  <depend>realtime_tools</depend>
  <depend>std_srvs</depend> -->

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>ament_cmake_gmock</test_depend>
  <test_depend>controller_manager</test_depend>
  <test_depend>hardware_interface</test_depend>
  <test_depend>ros2_control_test_assets</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>