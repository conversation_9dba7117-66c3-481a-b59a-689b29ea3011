// Copyright (c) 2022, Stogl Robotics Consulting UG (haftungsbeschränkt) (template)
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef MIT_CONTROLLER__MITCONTROLLER_HPP_
#define MIT_CONTROLLER__MITCONTROLLER_HPP_

#include <memory>
#include <string>
#include <vector>

#include "controller_interface/controller_interface.hpp"
// #include "MITController_parameters.hpp"
#include "mit_controller/visibility_control.h"
// #include "rclcpp_lifecycle/node_interfaces/lifecycle_node_interface.hpp"
// #include "rclcpp_lifecycle/state.hpp"
// #include "realtime_tools/realtime_buffer.h"
// #include "realtime_tools/realtime_publisher.h"
// #include "std_srvs/srv/set_bool.hpp"

// TODO(anyone): Replace with controller specific messages
// #include "control_msgs/msg/joint_controller_state.hpp"
// #include "control_msgs/msg/joint_jog.hpp"
#include <control_input_msgs/msg/inputs.hpp>
#include <std_msgs/msg/string.hpp>

#include <mit_controller/FSM/FSMState.h>
#include "FSM/StateBalanceTest.h"
#include "FSM/StateFixedDown.h"
#include "FSM/StateFixedStand.h"
#include "FSM/StateFreeStand.h"
#include "FSM/StatePassive.h"
#include "FSM/StateSwingTest.h"
#include "FSM/StateTrotting.h"

namespace mit_controller
{
struct FSMStateList
{
  std::shared_ptr<FSMState> invalid;            // 无效状态
  std::shared_ptr<StatePassive> passive;        // 被动状态
  std::shared_ptr<StateFixedDown> fixedDown;    // 固定蹲下
  std::shared_ptr<StateFixedStand> fixedStand;  // 固定站立
  std::shared_ptr<StateFreeStand> freeStand;    // 自由站立
  std::shared_ptr<StateTrotting> trotting;      // 小跑步态

  std::shared_ptr<StateSwingTest> swingTest;      // 摆腿测试
  std::shared_ptr<StateBalanceTest> balanceTest;  // 平衡测试
};                                                // 状态机状态列表

class MITController : public controller_interface::ControllerInterface
{
public:
  MIT_CONTROLLER__VISIBILITY_PUBLIC
  MITController();

  MIT_CONTROLLER__VISIBILITY_PUBLIC
  controller_interface::CallbackReturn on_init() override;

  MIT_CONTROLLER__VISIBILITY_PUBLIC
  controller_interface::InterfaceConfiguration command_interface_configuration() const override;

  MIT_CONTROLLER__VISIBILITY_PUBLIC
  controller_interface::InterfaceConfiguration state_interface_configuration() const override;

  MIT_CONTROLLER__VISIBILITY_PUBLIC
  controller_interface::CallbackReturn on_configure(const rclcpp_lifecycle::State& previous_state) override;

  MIT_CONTROLLER__VISIBILITY_PUBLIC
  controller_interface::CallbackReturn on_activate(const rclcpp_lifecycle::State& previous_state) override;

  MIT_CONTROLLER__VISIBILITY_PUBLIC
  controller_interface::CallbackReturn on_deactivate(const rclcpp_lifecycle::State& previous_state) override;

  MIT_CONTROLLER__VISIBILITY_PUBLIC
  controller_interface::return_type update(const rclcpp::Time& time, const rclcpp::Duration& period) override;

  // TODO(anyone): replace the state and command message types
  // using ControllerReferenceMsg = control_msgs::msg::JointJog;
  // using ControllerModeSrvType = std_srvs::srv::SetBool;
  // using ControllerStateMsg = control_msgs::msg::JointControllerState;

protected:
  // std::shared_ptr<MITController::ParamListener> param_listener_;
  // MITController::Params params_;

  // std::vector<std::string> state_joints_;
  // 控制器组件，用于管理控制器的状态和命令接口
  CtrlComponent ctrl_comp_;

  std::vector<std::string> joint_names_; // 机器人关节名称列表
  std::vector<std::string> feet_names_;  // 机器人足部名称列表
  std::vector<std::string> command_interface_types_; // 控制器命令接口类型列表
  std::vector<std::string> state_interface_types_;   // 控制器状态接口类型列表

  // IMU传感器名称和接口类型
  std::string imu_name_;
  std::vector<std::string> imu_interface_types_;
  std::string base_name_;
  std::string command_prefix_;
  
  

  // // Command subscribers and Controller State publisher
  // rclcpp::Subscription<ControllerReferenceMsg>::SharedPtr ref_subscriber_ = nullptr;
  // realtime_tools::RealtimeBuffer<std::shared_ptr<ControllerReferenceMsg>> input_ref_;

  // rclcpp::Service<ControllerModeSrvType>::SharedPtr set_slow_control_mode_service_;
  // realtime_tools::RealtimeBuffer<control_mode_type> control_mode_;

  // using ControllerStatePublisher = realtime_tools::RealtimePublisher<ControllerStateMsg>;

  // rclcpp::Publisher<ControllerStateMsg>::SharedPtr s_publisher_;
  // std::unique_ptr<ControllerStatePublisher> state_publisher_;

  // 命令订阅者，用于接收控制输入
  rclcpp::Subscription<control_input_msgs::msg::Inputs>::SharedPtr control_input_subscription_;
  // // 机器人描述订阅者，用于接收机器人描述信息
  rclcpp::Subscription<std_msgs::msg::String>::SharedPtr robot_description_subscription_;

  
  // 控制器命令接口映射，用于将命令接口与命令类型关联
  std::unordered_map<std::string, std::vector<std::reference_wrapper<hardware_interface::LoanedCommandInterface>>*>
      command_interface_map_ = { { "effort", &ctrl_comp_.joint_torque_command_interface_ },
                                 { "position", &ctrl_comp_.joint_position_command_interface_ },
                                 { "velocity", &ctrl_comp_.joint_velocity_command_interface_ },
                                 { "kp", &ctrl_comp_.joint_kp_command_interface_ },
                                 { "kd", &ctrl_comp_.joint_kd_command_interface_ } };

  //  状态接口映射，用于将状态接口与状态类型关联
  std::unordered_map<std::string, std::vector<std::reference_wrapper<hardware_interface::LoanedStateInterface>>*>
      state_interface_map_ = { { "position", &ctrl_comp_.joint_position_state_interface_ },
                               { "effort", &ctrl_comp_.joint_effort_state_interface_ },
                               { "velocity", &ctrl_comp_.joint_velocity_state_interface_ } };

  FSMMode mode_ = FSMMode::NORMAL;
  std::string state_name_;
  FSMStateName next_state_name_ = FSMStateName::INVALID;
  FSMStateList state_list_;
  std::shared_ptr<FSMState> current_state_;
  std::shared_ptr<FSMState> next_state_;

  std::chrono::time_point<std::chrono::steady_clock> last_update_time_;
  double update_frequency_;

  std::shared_ptr<FSMState> getNextState(FSMStateName stateName) const;

  // private:
  //   // callback for topic interface
  //   MIT_CONTROLLER__VISIBILITY_LOCAL
  //   void reference_callback(const std::shared_ptr<ControllerReferenceMsg> msg);
};

}  // namespace mit_controller

#endif  // MIT_CONTROLLER__MITCONTROLLER_HPP_