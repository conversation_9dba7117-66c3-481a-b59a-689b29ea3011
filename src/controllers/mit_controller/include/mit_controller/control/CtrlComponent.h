//
// Created by <PERSON><PERSON> on 24-9-10.
//

#ifndef INTERFACE_H
#define INTERFACE_H

#include <vector>
#include <hardware_interface/loaned_command_interface.hpp>
#include <hardware_interface/loaned_state_interface.hpp>
#include <control_input_msgs/msg/inputs.hpp>
#include <mit_controller/gait/WaveGenerator.h>
#include <mit_controller/robot/QuadrupedRobot.h>

#include "BalanceController/BalanceCtrl.h"
#include "Estimator/Estimator.h"
#include "ConvexMPC/ConvexMPCCtrl.h"

struct CtrlComponent {
    // 命令接口
    std::vector<std::reference_wrapper<hardware_interface::LoanedCommandInterface> >
    joint_torque_command_interface_; // 力矩接口
    std::vector<std::reference_wrapper<hardware_interface::LoanedCommandInterface> >
    joint_position_command_interface_; // 位置接口
    std::vector<std::reference_wrapper<hardware_interface::LoanedCommandInterface> >
    joint_velocity_command_interface_; // 速度接口
    std::vector<std::reference_wrapper<hardware_interface::LoanedCommandInterface> >
    joint_kp_command_interface_; // 位置增益接口
    std::vector<std::reference_wrapper<hardware_interface::LoanedCommandInterface> >
    joint_kd_command_interface_; // 速度增益接口

    // 状态接口
    std::vector<std::reference_wrapper<hardware_interface::LoanedStateInterface> >
    joint_effort_state_interface_; // 力矩状态接口
    std::vector<std::reference_wrapper<hardware_interface::LoanedStateInterface> >
    joint_position_state_interface_; // 位置状态接口
    std::vector<std::reference_wrapper<hardware_interface::LoanedStateInterface> >
    joint_velocity_state_interface_; // 速度状态接口

    std::vector<std::reference_wrapper<hardware_interface::LoanedStateInterface> >
    imu_state_interface_; // IMU状态接口
    
    // 控制输入
    control_input_msgs::msg::Inputs control_inputs_;
    int frequency_{};
    
    // 机器人模型  std::shared_ptr 智能指针，指向QuadrupedRobot对象
    std::shared_ptr<QuadrupedRobot> robot_model_;
    // 传感器估计器
    std::shared_ptr<Estimator> estimator_;

    // 平衡控制器
    std::shared_ptr<BalanceCtrl> balance_ctrl_;

    // MPC控制器
    std::shared_ptr<ConvexMPCCtrl> convex_mpc_ctrl_; 

    // 波形生成器
    std::shared_ptr<WaveGenerator> wave_generator_;

    CtrlComponent() = default; // 使用默认构造函数

    // 初始化，清空接口
    void clear() {
        joint_torque_command_interface_.clear();
        joint_position_command_interface_.clear();
        joint_velocity_command_interface_.clear();
        joint_kd_command_interface_.clear();
        joint_kp_command_interface_.clear();

        joint_effort_state_interface_.clear();
        joint_position_state_interface_.clear();
        joint_velocity_state_interface_.clear();

        imu_state_interface_.clear();
    }
};

#endif //INTERFACE_H