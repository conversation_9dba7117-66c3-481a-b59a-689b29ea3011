#ifndef CONVEXMPCCONTROLLER_H
#define CONVEXMPCCONTROLLER_H

#include <memory>

#include "mit_controller/common/mathTypes.h"
class QuadrupedRobot;

class ConvexMPCCtrl{
public:
    explicit ConvexMPCCtrl(const std::shared_ptr<QuadrupedRobot>& robot);

    ~ConvexMPCCtrl() = default;
     /**
     * Calculate the desired feet end force
     * @param ddPcd desired body acceleration
     * @param dWbd desired body angular acceleration
     * @param rot_matrix current body rotation matrix
     * @param feet_pos_2_body feet positions to body under world frame
     * @param contact feet contact, 1 for contact, 0 for no contact
     * @return
     */

    Vec34 calF(const Vec13 &x0_, const Vec3 &dPcd, const Vec3 &Wbd, const RotMat &R_yaw, const RotMat &rot_matrix,
               const Vec34 &feet_pos_2_body, const VecInt4 &contact, const double &dt_, const Vec3& pcd);
private:

    void calMatrixAcBc(const Vec34 &feet_pos_2_body, const RotMat &R_yaw, const RotMat &rot_matrix);


    void calConstraints(const VecInt4 &contact);

    void Continuous2Discrete(const double &dt_); // 离散化

    void calAqpBqp();

    void solveQP();


    Eigen::Matrix<double, -1, -1> H_qpoases;
    Eigen::VectorXd fT_qpoases;
    Eigen::Matrix<double, 13, 1> Qi_;
    Eigen::Matrix<double, 12, 1> Ri_;
    Mat3 Ib_;
    // Vec6 bd_;
    Vec3 g_, pcb_;
    Vec12 F_, F_prev_;
    double mass_, alpha_, beta_, friction_ratio_;
    Eigen::MatrixXd CE_, CI_; // QP约束矩阵
    Eigen::VectorXd ce0_, ci0_; // QP约束向量
    // Eigen::Matrix<double, 6, 12> A_;    
    Eigen::Matrix<double, 5, 3> friction_mat_; // 摩擦系数矩阵

    const int Horizon_ = 10; // MPC窗口大小
    Eigen::Matrix<double, 13, 13> Ac_; // 单刚体连续动力学Ac矩阵，状态为：3角度，3位置，3角速度，3速度，1重力加速度g
    Eigen::Matrix<double, 13, 12> Bc_; // 单刚体连续动力学Bc矩阵
    Eigen::Matrix<double, 13, 13> Ad_; // 单刚体离散动力学Ad矩阵
    Eigen::Matrix<double, 13, 12> Bd_; // 单刚体离散动力学Bd矩阵
    Eigen::Matrix<double, Eigen::Dynamic, 13> Aqp_;
    MatX Bqp_;
    

};

#endif // CONVEXMPCCONTROLLER_H