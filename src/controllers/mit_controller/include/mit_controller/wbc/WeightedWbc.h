//
// Created by user on 2024-12-19.
//

#ifndef MIT_CONTROLLER_WEIGHTED_WBC_H
#define MIT_CONTROLLER_WEIGHTED_WBC_H

#include "WbcBase.h"
#include <Eigen/Dense>

namespace mit_controller {

/**
 * @brief 加权WBC实现
 * 使用加权最小二乘法求解WBC问题:
 * 
 * min  ||A_task * x - b_task||^2_W + regularization
 * s.t. A_eq * x = b_eq     (等式约束)
 *      A_ineq * x <= b_ineq (不等式约束)
 * 
 * 其中 x = [qdd_floating, F_contact, tau_joint]^T
 */
class WeightedWbc : public WbcBase {
public:
    /**
     * @brief 构造函数
     * @param robot_model 机器人模型
     */
    explicit WeightedWbc(std::shared_ptr<QuadrupedRobot> robot_model);
    
    /**
     * @brief WBC更新函数
     */
    Vec update(const Vec13& desired_state,
               const Vec12& desired_input,
               const Vec13& measured_state,
               const VecInt4& contact_states,
               double dt) override;
    
    /**
     * @brief 设置任务权重
     */
    void setTaskWeights(double body_accel_weight,
                        double swing_leg_weight,
                        double contact_force_weight);
    
    /**
     * @brief 设置正则化参数
     */
    void setRegularization(double reg_floating_base,
                           double reg_contact_force,
                           double reg_joint_torque);

private:
    /**
     * @brief 构建约束
     */
    void formulateConstraints(const VecInt4& contact_states,
                              Mat& A_eq, Vec& b_eq,
                              Mat& A_ineq, Vec& b_ineq);
    
    /**
     * @brief 构建加权任务
     */
    void formulateWeightedTasks(const Vec13& desired_state,
                                const Vec12& desired_input,
                                const Vec13& measured_state,
                                const Vec34& desired_foot_pos,
                                const Vec34& desired_foot_vel,
                                const VecInt4& contact_states,
                                double dt,
                                Mat& A_task, Vec& b_task, Mat& W_task);
    
    /**
     * @brief 求解QP问题
     * min  ||A_task * x - b_task||^2_W + x^T * R * x
     * s.t. A_eq * x = b_eq
     *      A_ineq * x <= b_ineq
     */
    Vec solveQP(const Mat& A_task, const Vec& b_task, const Mat& W_task,
                const Mat& A_eq, const Vec& b_eq,
                const Mat& A_ineq, const Vec& b_ineq,
                const Mat& R);
    
    /**
     * @brief 使用伪逆求解（备用方法）
     */
    Vec solvePseudoInverse(const Mat& A_task, const Vec& b_task, const Mat& W_task,
                           const Mat& A_eq, const Vec& b_eq);

private:
    // 正则化参数
    double reg_floating_base_ = 1e-6;
    double reg_contact_force_ = 1e-5;
    double reg_joint_torque_ = 1e-4;
    
    // 期望足端轨迹（由步态生成器提供）
    Vec34 desired_foot_positions_;
    Vec34 desired_foot_velocities_;
    
    // QP求解器相关
    bool use_qp_solver_ = true;  // 是否使用QP求解器，否则使用伪逆
    
    // 上一次的解（用于初始化）
    Vec last_solution_;
    
public:
    /**
     * @brief 设置期望足端轨迹
     */
    void setDesiredFootTrajectories(const Vec34& foot_positions, const Vec34& foot_velocities) {
        desired_foot_positions_ = foot_positions;
        desired_foot_velocities_ = foot_velocities;
    }
    
    /**
     * @brief 启用/禁用QP求解器
     */
    void enableQPSolver(bool enable) { use_qp_solver_ = enable; }
};

} // namespace mit_controller

#endif // MIT_CONTROLLER_WEIGHTED_WBC_H
