//
// Created by user on 2024-12-19.
//

#ifndef MIT_CONTROLLER_WBC_BASE_H
#define MIT_CONTROLLER_WBC_BASE_H

#include <memory>
#include <vector>
#include <mit_controller/common/mathTypes.h>
#include <mit_controller/robot/QuadrupedRobot.h>

namespace mit_controller {

/**
 * @brief WBC任务结构体
 */
struct WbcTask {
    Mat A;      // 任务矩阵
    Vec b;      // 任务向量
    Mat D;      // 不等式约束矩阵
    Vec f;      // 不等式约束向量
    double weight; // 任务权重
    
    WbcTask() : weight(1.0) {}
    
    WbcTask(const Mat& A_in, const Vec& b_in, double w = 1.0) 
        : A(A_in), b(b_in), weight(w) {}
    
    WbcTask(const Mat& A_in, const Vec& b_in, const Mat& D_in, const Vec& f_in, double w = 1.0)
        : A(A_in), b(b_in), D(D_in), f(f_in), weight(w) {}
    
    // 任务组合操作符
    WbcTask operator+(const WbcTask& other) const;
    WbcTask operator*(double weight_factor) const;
};

/**
 * @brief WBC基础类
 * 决策变量: x = [qdd_floating, F_contact, tau_joint]^T
 * 其中:
 * - qdd_floating: 6维浮动基座加速度 [linear_acc, angular_acc]
 * - F_contact: 12维接触力 [F_leg0, F_leg1, F_leg2, F_leg3]
 * - tau_joint: 12维关节力矩
 */
class WbcBase {
public:
    /**
     * @brief 构造函数
     * @param robot_model 机器人模型
     */
    explicit WbcBase(std::shared_ptr<QuadrupedRobot> robot_model);
    
    virtual ~WbcBase() = default;

    /**
     * @brief WBC更新函数
     * @param desired_state 期望状态 (13维: rpy, pos, omega, vel, g)
     * @param desired_input MPC输出的期望接触力 (12维)
     * @param measured_state 测量状态
     * @param contact_states 接触状态 (4维)
     * @param dt 时间步长
     * @return 关节控制命令 (位置、速度、力矩)
     */
    virtual Vec update(const Vec13& desired_state,
                       const Vec12& desired_input,
                       const Vec13& measured_state,
                       const VecInt4& contact_states,
                       double dt) = 0;

protected:
    /**
     * @brief 更新机器人状态
     */
    void updateRobotState(const Vec13& measured_state, const VecInt4& contact_states);
    
    /**
     * @brief 构建浮动基座动力学约束
     * M * qdd + h = S^T * tau + J^T * F
     * 其中 S^T * tau 是关节力矩对广义坐标的贡献，J^T * F 是接触力的贡献
     */
    WbcTask formulateFloatingBaseDynamics();
    
    /**
     * @brief 构建关节力矩限制约束
     * -tau_max <= tau <= tau_max
     */
    WbcTask formulateTorqueLimits();
    
    /**
     * @brief 构建摩擦锥约束
     * 对于每个接触足: |F_x|, |F_y| <= mu * F_z, F_z >= 0
     */
    WbcTask formulateFrictionCone(const VecInt4& contact_states);
    
    /**
     * @brief 构建非接触运动约束
     * 对于摆动腿: F = 0
     */
    WbcTask formulateNoContactMotion(const VecInt4& contact_states);
    
    /**
     * @brief 构建身体加速度任务
     * 跟踪期望的身体线性和角加速度
     */
    WbcTask formulateBodyAccelTask(const Vec13& desired_state, 
                                   const Vec13& measured_state, 
                                   double dt);
    
    /**
     * @brief 构建摆动腿任务
     * 跟踪期望的足端位置和速度
     */
    WbcTask formulateSwingLegTask(const Vec34& desired_foot_pos,
                                  const Vec34& desired_foot_vel,
                                  const VecInt4& contact_states);
    
    /**
     * @brief 构建接触力任务
     * 跟踪MPC输出的期望接触力
     */
    WbcTask formulateContactForceTask(const Vec12& desired_forces,
                                      const VecInt4& contact_states);
    
    /**
     * @brief 获取决策变量维度
     */
    size_t getDecisionVarDim() const { return decision_var_dim_; }
    
    /**
     * @brief 从WBC解中提取关节控制命令
     */
    void extractJointCommands(const Vec& wbc_solution,
                              Vec12& joint_positions,
                              Vec12& joint_velocities, 
                              Vec12& joint_torques,
                              double dt);

protected:
    std::shared_ptr<QuadrupedRobot> robot_model_;
    
    // 决策变量维度
    static constexpr size_t floating_base_dim_ = 6;   // 浮动基座加速度
    static constexpr size_t contact_force_dim_ = 12;  // 接触力
    static constexpr size_t joint_torque_dim_ = 12;   // 关节力矩
    static constexpr size_t decision_var_dim_ = floating_base_dim_ + contact_force_dim_ + joint_torque_dim_;
    
    // 当前机器人状态
    Vec3 current_rpy_, current_pos_, current_omega_, current_vel_;
    Vec12 current_joint_pos_, current_joint_vel_;
    VecInt4 current_contact_states_;
    
    // 动力学矩阵
    Mat mass_matrix_;           // 质量矩阵 M
    Vec nonlinear_effects_;     // 非线性项 h (科里奥利力+重力)
    Mat34 jacobians_[4];        // 足端雅可比矩阵
    
    // 约束参数
    double friction_coeff_ = 0.4;      // 摩擦系数
    Vec3 torque_limits_ = Vec3(33.5, 33.5, 33.5);  // 关节力矩限制 [hip, thigh, calf]
    
    // 任务权重
    double weight_body_accel_ = 1000.0;
    double weight_swing_leg_ = 1000.0;
    double weight_contact_force_ = 1.0;
    
private:
    /**
     * @brief 更新动力学矩阵
     */
    void updateDynamics();
    
    /**
     * @brief 计算足端雅可比矩阵
     */
    void updateJacobians();
};

} // namespace mit_controller

#endif // MIT_CONTROLLER_WBC_BASE_H
