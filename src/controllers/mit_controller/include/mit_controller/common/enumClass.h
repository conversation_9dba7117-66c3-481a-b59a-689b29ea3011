//
// Created by tlab-uav on 24-9-6.
//

#ifndef ENUMCLASS_H
#define ENUMCLASS_H

enum class FSMStateName {
    // EXIT,
    INVALID,
    PASSIVE,
    FIXEDDOWN,
    FIXEDSTAND,
    FREESTAND,
    TROTTING,

    SWINGTEST,
    BALANCETEST,
};

enum class FSMMode {
    NORMAL, // 0
    CHANGE  // 1
};

enum class FrameType {
    BODY,
    HIP,
    GLOBAL
};

enum class WaveStatus { 
    STANCE_ALL, // 波形站立
    SWING_ALL,  // 波形摆动
    WAVE_ALL    // 波形
};

#endif //ENUMCLASS_H