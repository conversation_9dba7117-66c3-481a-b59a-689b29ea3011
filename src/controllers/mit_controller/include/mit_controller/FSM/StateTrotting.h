//
// Created by tlab-uav on 24-9-18.
//

#ifndef STATETROTTING_H
#define STATETROTTING_H
#include <mit_controller/gait/GaitGenerator.h>
#include <mit_controller/safety/SafetyChecker.h>
#include "FSMState.h"
#include <mit_controller/wbc/WeightedWbc.h>

class StateTrotting final : public FSMState {
public:
    explicit StateTrotting(CtrlComponent &ctrlComp);

    void enter() override;

    void run() override;

    void exit() override;

    FSMStateName checkChange() override;

private:
    void getUserCmd();

    void calcCmd();

    /**
    * Calculate the torque command
    */
    void calcTau();
    /**
    * Calculate the torque command using WBC
    */
    void calcTauWithWBC();
    /**
    * Calculate the joint space velocity and acceleration
    */
    void calcQQd();

    /**
    * Calculate the PD gain for the joints
    */
    void calcGain() const;

    /**
     * Check whether the robot should take a step or not
     * @return
     */
    bool checkStepOrNot();

    // std::shared_ptr<类> &是一个引用类型的智能指针，，允许你通过引用来操作原始的智能指针，而不是创建新的智能指针副本。
    std::shared_ptr<Estimator> &estimator_;             
    std::shared_ptr<QuadrupedRobot> &robot_model_;
    // 平衡控制器
    std::shared_ptr<BalanceCtrl> &balance_ctrl_;
    // cMPC控制器
    std::shared_ptr<ConvexMPCCtrl> &convex_mpc_ctrl_;
    std::shared_ptr<WaveGenerator> &wave_generator_;

    GaitGenerator gait_generator_;

    // Robot State
    Vec3 pos_body_, vel_body_;
    Vec3 rpy_, w_body_; 
    RotMat B2G_RotMat, G2B_RotMat, R_yaw;
    Vec13 x0_;

    // Robot command
    Vec3 pcd_; 
    Vec3 vel_target_, v_cmd_body_;
    double dt_;
    double yaw_cmd_{}, d_yaw_cmd_{}, d_yaw_cmd_past_{};
    Vec3 w_cmd_global_;
    Vec34 pos_feet_global_goal_, vel_feet_global_goal_;
    RotMat Rd;
    

    // Control Parameters
    double gait_height_;
    Vec3 pos_error_, vel_error_;
    Mat3 Kpp, Kdp, Kd_w_;
    double kp_w_;
    Mat3 Kp_swing_, Kd_swing_;
    Vec2 v_x_limit_, v_y_limit_, w_yaw_limit_;
    
    // Safety Checker
    // 在StateTrotting.h中
    std::shared_ptr<mit_controller::SafetyChecker> safety_checker_;
    // WBC Controller
    // 使用完整命名空间
    std::shared_ptr<mit_controller::WeightedWbc> wbc_controller_;
    bool use_wbc_ = false;  // 是否启用WBC控制器

    bool first_run_ = true;
};


#endif //STATETROTTING_H
