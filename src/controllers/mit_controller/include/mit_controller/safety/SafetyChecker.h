//
// Created by user on 2024-12-19.
//

#ifndef MIT_CONTROLLER_SAFETY_CHECKER_H
#define MIT_CONTROLLER_SAFETY_CHECKER_H

#include <iostream>
#include <cmath>
#include <mit_controller/common/mathTypes.h>

namespace mit_controller {

/**
 * @brief 安全检查器类，用于检查机器人状态和控制命令的安全性
 */
class SafetyChecker {
public:
    /**
     * @brief 构造函数
     * @param max_roll_angle 最大允许的roll角度 (弧度)
     * @param max_pitch_angle 最大允许的pitch角度 (弧度)
     * @param max_joint_torque 最大允许的关节力矩 (Nm)
     * @param max_joint_velocity 最大允许的关节速度 (rad/s)
     * @param max_foot_position 最大允许的足端位置 (m)
     */
    SafetyChecker(double max_roll_angle = M_PI_4,      // 45度
                  double max_pitch_angle = M_PI_4,     // 45度
                  double max_joint_torque = 33.5,     // Go2电机最大力矩
                  double max_joint_velocity = 21.0,   // Go2电机最大速度
                  double max_foot_position = 0.4);    // 最大足端位置

    /**
     * @brief 检查机器人姿态是否安全
     * @param rpy 当前姿态角 [roll, pitch, yaw]
     * @return true if safe, false otherwise
     */
    bool checkOrientation(const Vec3& rpy) const;

    /**
     * @brief 检查关节力矩是否超限
     * @param joint_torques 12维关节力矩向量
     * @return true if safe, false otherwise
     */
    bool checkJointTorques(const Vec12& joint_torques) const;

    /**
     * @brief 检查关节速度是否超限
     * @param joint_velocities 12维关节速度向量
     * @return true if safe, false otherwise
     */
    bool checkJointVelocities(const Vec12& joint_velocities) const;

    /**
     * @brief 检查足端位置是否在安全范围内
     * @param foot_positions 4x3足端位置矩阵 (相对于body frame)
     * @return true if safe, false otherwise
     */
    bool checkFootPositions(const Vec34& foot_positions) const;

    /**
     * @brief 检查机器人高度是否安全
     * @param body_height 机器人身体高度
     * @param min_height 最小安全高度
     * @param max_height 最大安全高度
     * @return true if safe, false otherwise
     */
    bool checkBodyHeight(double body_height, double min_height = 0.1, double max_height = 0.5) const;

    /**
     * @brief 综合安全检查
     * @param rpy 姿态角
     * @param joint_torques 关节力矩
     * @param joint_velocities 关节速度
     * @param foot_positions 足端位置
     * @param body_height 身体高度
     * @return true if all checks pass, false otherwise
     */
    bool checkOverall(const Vec3& rpy,
                      const Vec12& joint_torques,
                      const Vec12& joint_velocities,
                      const Vec34& foot_positions,
                      double body_height) const;

    /**
     * @brief 限制关节力矩到安全范围
     * @param joint_torques 输入输出的关节力矩
     */
    void saturateJointTorques(Vec12& joint_torques) const;

    /**
     * @brief 限制足端位置到安全范围
     * @param foot_positions 输入输出的足端位置
     */
    void saturateFootPositions(Vec34& foot_positions) const;

    // Getter和Setter方法
    void setMaxRollAngle(double angle) { max_roll_angle_ = angle; }
    void setMaxPitchAngle(double angle) { max_pitch_angle_ = angle; }
    void setMaxJointTorque(double torque) { max_joint_torque_ = torque; }
    void setMaxJointVelocity(double velocity) { max_joint_velocity_ = velocity; }
    void setMaxFootPosition(double position) { max_foot_position_ = position; }

    double getMaxRollAngle() const { return max_roll_angle_; }
    double getMaxPitchAngle() const { return max_pitch_angle_; }
    double getMaxJointTorque() const { return max_joint_torque_; }
    double getMaxJointVelocity() const { return max_joint_velocity_; }
    double getMaxFootPosition() const { return max_foot_position_; }

private:
    double max_roll_angle_;      // 最大roll角度
    double max_pitch_angle_;     // 最大pitch角度
    double max_joint_torque_;    // 最大关节力矩
    double max_joint_velocity_;  // 最大关节速度
    double max_foot_position_;   // 最大足端位置

    /**
     * @brief 饱和函数
     * @param value 输入值
     * @param min_val 最小值
     * @param max_val 最大值
     * @return 饱和后的值
     */
    template<typename T>
    T saturate(T value, T min_val, T max_val) const {
        return std::max(min_val, std::min(max_val, value));
    }
};

} // namespace mit_controller

#endif // MIT_CONTROLLER_SAFETY_CHECKER_H
