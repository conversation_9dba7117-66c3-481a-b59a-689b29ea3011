// Copyright (c) 2022, Stogl Robotics Consulting UG (haftungsbeschränkt) (template)
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "mit_controller/MITController.hpp"
#include "mit_controller/FSM/StatePassive.h"
#include "mit_controller/robot/QuadrupedRobot.h"

// #include <limits>

// #include "controller_interface/helpers.hpp"

namespace mit_controller
{
MITController::MITController() : controller_interface::ControllerInterface()
{
}

controller_interface::CallbackReturn MITController::on_init()
{
  try
  {
    // Hardware Parameters
    joint_names_ = auto_declare<std::vector<std::string> >("joints", joint_names_);
    command_interface_types_ = auto_declare<std::vector<std::string> >("command_interfaces", command_interface_types_);
    state_interface_types_ = auto_declare<std::vector<std::string> >("state_interfaces", state_interface_types_);
    imu_name_ = auto_declare<std::string>("imu_name", imu_name_);
    base_name_ = auto_declare<std::string>("base_name", base_name_);
    imu_interface_types_ = auto_declare<std::vector<std::string> >("imu_interfaces", state_interface_types_);
    command_prefix_ = auto_declare<std::string>("command_prefix", command_prefix_);
    feet_names_ = auto_declare<std::vector<std::string> >("feet_names", feet_names_);

    get_node()->get_parameter("update_rate", ctrl_comp_.frequency_);
    RCLCPP_INFO(get_node()->get_logger(), "Controller Manager Update Rate: %d Hz", ctrl_comp_.frequency_);

    ctrl_comp_.estimator_ = std::make_shared<Estimator>(ctrl_comp_);
    ctrl_comp_.safety_checker_ = std::make_shared<mit_controller::SafetyChecker>(
        M_PI_4,    // max_roll_angle (45度)
        M_PI_4,    // max_pitch_angle (45度)
        40,      // max_joint_torque (Go2电机最大力矩)
        21.0,      // max_joint_velocity (Go2电机最大速度)
        0.4        // max_foot_position
    );

  }
  catch (const std::exception& e)
  {
    fprintf(stderr, "Exception thrown during controller's init with message: %s \n", e.what());
    return controller_interface::CallbackReturn::ERROR;
  }

  return controller_interface::CallbackReturn::SUCCESS;
}

controller_interface::CallbackReturn MITController::on_configure(const rclcpp_lifecycle::State& /*previous_state*/)
{
  // Reference Subscriber
  control_input_subscription_ = get_node()->create_subscription<control_input_msgs::msg::Inputs>(
      "/control_input", 10, [this](const control_input_msgs::msg::Inputs::SharedPtr msg) {
        // Handle message
        ctrl_comp_.control_inputs_.command = msg->command;
        ctrl_comp_.control_inputs_.lx = msg->lx;
        ctrl_comp_.control_inputs_.ly = msg->ly;
        ctrl_comp_.control_inputs_.rx = msg->rx;
        ctrl_comp_.control_inputs_.ry = msg->ry;
      });

  robot_description_subscription_ = get_node()->create_subscription<std_msgs::msg::String>(
      "/robot_description", rclcpp::QoS(rclcpp::KeepLast(1)).transient_local(),
      [this](const std_msgs::msg::String::SharedPtr msg) {
        ctrl_comp_.robot_model_ = std::make_shared<QuadrupedRobot>(ctrl_comp_, msg->data, feet_names_, base_name_);
        ctrl_comp_.balance_ctrl_ = std::make_shared<BalanceCtrl>(ctrl_comp_.robot_model_);
        ctrl_comp_.convex_mpc_ctrl_ = std::make_shared<ConvexMPCCtrl>(ctrl_comp_.robot_model_);
      });

  ctrl_comp_.wave_generator_ = std::make_shared<WaveGenerator>(0.45, 0.5, Vec4(0, 0.5, 0.5, 0)); // Trot

  // ctrl_comp_.wave_generator_ = std::make_shared<WaveGenerator>(1.1, 0.75, Vec4(0, 0.25, 0.5, 0.75));  //Crawl, only for sim
  // ctrl_comp_.wave_generator_ = std::make_shared<WaveGenerator>(0.4, 0.6, Vec4(0, 0.5, 0.5, 0));  //Walking Trot, only for sim
  // ctrl_comp_.wave_generator_ = std::make_shared<WaveGenerator>(0.4, 0.35, Vec4(0, 0.5, 0.5, 0));  //Running Trot, only for sim
  // ctrl_comp_.wave_generator_ = std::make_shared<WaveGenerator>(0.4, 0.7, Vec4(0, 0, 0, 0));  //Pronk, only for sim

  RCLCPP_INFO(get_node()->get_logger(), "configure successful");
  return controller_interface::CallbackReturn::SUCCESS;
}

controller_interface::InterfaceConfiguration MITController::command_interface_configuration() const
{
  controller_interface::InterfaceConfiguration command_interfaces_config;
  command_interfaces_config.type = controller_interface::interface_configuration_type::INDIVIDUAL;

  command_interfaces_config.names.reserve(joint_names_.size() * command_interface_types_.size());
  for (const auto& joint_name : joint_names_)
  {
    for (const auto& interface_type : command_interface_types_)
    {
      if (!command_prefix_.empty())
      {
        command_interfaces_config.names.push_back(command_prefix_ + "/" + joint_name + "/" += interface_type);
      }
      else
      {
        command_interfaces_config.names.push_back(joint_name + "/" += interface_type);
      }
    }
  }

  return command_interfaces_config;
}

controller_interface::InterfaceConfiguration MITController::state_interface_configuration() const
{
  controller_interface::InterfaceConfiguration state_interfaces_config;
  state_interfaces_config.type = controller_interface::interface_configuration_type::INDIVIDUAL;

  state_interfaces_config.names.reserve(joint_names_.size() * state_interface_types_.size());
  for (const auto& joint_name : joint_names_)
  {
    for (const auto& interface_type : state_interface_types_)
    {
      state_interfaces_config.names.push_back(joint_name + "/" += interface_type);
    }
  }

  for (const auto& interface_type : imu_interface_types_)
  {
    state_interfaces_config.names.push_back(imu_name_ + "/" += interface_type);
  }

  return state_interfaces_config;
}

controller_interface::CallbackReturn MITController::on_activate(const rclcpp_lifecycle::State& /*previous_state*/)
{
  // TODO(anyone): if you have to manage multiple interfaces that need to be sorted check
  // `on_activate` method in `JointTrajectoryController` for examplary use of
  // `controller_interface::get_ordered_interfaces` helper function

  // clear out vectors in case of restart
  ctrl_comp_.clear();

  // assign command interfaces
  for (auto& interface : command_interfaces_)
  {
    std::string interface_name = interface.get_interface_name();
    if (const size_t pos = interface_name.find('/'); pos != std::string::npos)
    {
      command_interface_map_[interface_name.substr(pos + 1)]->push_back(interface);
    }
    else
    {
      command_interface_map_[interface_name]->push_back(interface);
    }
  }

  // assign state interfaces
  for (auto& interface : state_interfaces_)
  {
    if (interface.get_prefix_name() == imu_name_)
    {
      ctrl_comp_.imu_state_interface_.emplace_back(interface);
    }
    else
    {
      state_interface_map_[interface.get_interface_name()]->push_back(interface);
    }
  }

  // Create FSM List
  state_list_.passive = std::make_shared<StatePassive>(ctrl_comp_);
  state_list_.fixedDown = std::make_shared<StateFixedDown>(ctrl_comp_);
  state_list_.fixedStand = std::make_shared<StateFixedStand>(ctrl_comp_);
  state_list_.swingTest = std::make_shared<StateSwingTest>(ctrl_comp_);
  state_list_.freeStand = std::make_shared<StateFreeStand>(ctrl_comp_);
  state_list_.balanceTest = std::make_shared<StateBalanceTest>(ctrl_comp_);
  state_list_.trotting = std::make_shared<StateTrotting>(ctrl_comp_);

  // Initialize FSM
  current_state_ = state_list_.passive;
  current_state_->enter();
  next_state_ = current_state_;
  next_state_name_ = current_state_->state_name;
  mode_ = FSMMode::NORMAL;

  return controller_interface::CallbackReturn::SUCCESS;
}

controller_interface::CallbackReturn MITController::on_deactivate(const rclcpp_lifecycle::State& /*previous_state*/)
{
  release_interfaces();
  return controller_interface::CallbackReturn::SUCCESS;
}

controller_interface::return_type MITController::update(const rclcpp::Time& time, const rclcpp::Duration& /*period*/)
{
  if (ctrl_comp_.robot_model_ == nullptr)
  {
    return controller_interface::return_type::OK;
  }

  // 更新模型和波生成器, 更新估计器
  ctrl_comp_.robot_model_->update();
  ctrl_comp_.wave_generator_->update();
  ctrl_comp_.estimator_->update();


  // 获取状态
  rpy_ = ctrl_comp_.estimator_->getrpy(); // 获取当前姿态角
  pos_body_ = ctrl_comp_.estimator_->getPosition();


  // 安全检查 - 检查当前状态
  if (!ctrl_comp_.safety_checker_->checkOrientation(rpy_)) {
      std::cerr << "[StateTrotting] Orientation safety check failed, switching to passive mode!" << std::endl;
      // return; // 或者切换到安全状态
      mode_ = FSMMode::CHANGE;
      next_state_ = state_list_.passive;
  }
  // 安全检查 - 检查当前高度，我把它放到了stateTrotting中，放在这，passive状态也会检查
  // if (!ctrl_comp_.safety_checker_->checkBodyHeight(pos_body_(2))) {
  //     std::cerr << "[StateTrotting] Body height safety check failed!" << std::endl;
  //     // return; // 或者切换到安全状态
  //     mode_ = FSMMode::CHANGE;
  //     next_state_ = state_list_.passive;
  // }
  
  // Finite State Machine，根据当前状态和下一个状态进行状态切换
  if (mode_ == FSMMode::NORMAL)
  {
    current_state_->run();  // FSM中不同状态下的run()函数，用于执行不同的控制逻辑
    next_state_name_ = current_state_->checkChange();
    if (next_state_name_ != current_state_->state_name)
    {
      mode_ = FSMMode::CHANGE;
      next_state_ = getNextState(next_state_name_);
      RCLCPP_INFO(get_node()->get_logger(), "Switched from %s to %s", current_state_->state_name_string.c_str(),
                  next_state_->state_name_string.c_str());
    }
  }
  else if (mode_ == FSMMode::CHANGE)
  {
    current_state_->exit();
    current_state_ = next_state_;

    current_state_->enter();
    mode_ = FSMMode::NORMAL;
  }

  return controller_interface::return_type::OK;
}

std::shared_ptr<FSMState> MITController::getNextState(const FSMStateName stateName) const
{
  switch (stateName)
  {
    case FSMStateName::INVALID:
      return state_list_.invalid;
    case FSMStateName::PASSIVE:
      return state_list_.passive;
    case FSMStateName::FIXEDDOWN:
      return state_list_.fixedDown;
    case FSMStateName::FIXEDSTAND:
      return state_list_.fixedStand;
    case FSMStateName::FREESTAND:
      return state_list_.freeStand;
    case FSMStateName::TROTTING:
      return state_list_.trotting;
    case FSMStateName::SWINGTEST:
      return state_list_.swingTest;
    case FSMStateName::BALANCETEST:
      return state_list_.balanceTest;
    default:
      return state_list_.invalid;
  }
}

}  // namespace mit_controller

#include "pluginlib/class_list_macros.hpp"

PLUGINLIB_EXPORT_CLASS(mit_controller::MITController, controller_interface::ControllerInterface)