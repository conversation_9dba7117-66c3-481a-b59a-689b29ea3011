//
// Created by user on 2024-12-19.
//

#include "mit_controller/wbc/WbcBase.h"
#include <iostream>

namespace mit_controller {

WbcTask WbcTask::operator+(const WbcTask& other) const {
    WbcTask result;
    
    // 合并等式约束
    if (A.rows() > 0 && other.A.rows() > 0) {
        result.A.resize(A.rows() + other.A.rows(), std::max(A.cols(), other.A.cols()));
        result.b.resize(A.rows() + other.A.rows());
        
        result.A.topRows(A.rows()) = A;
        result.A.bottomRows(other.A.rows()) = other.A;
        result.b.head(A.rows()) = b;
        result.b.tail(other.A.rows()) = other.b;
    } else if (A.rows() > 0) {
        result.A = A;
        result.b = b;
    } else if (other.A.rows() > 0) {
        result.A = other.A;
        result.b = other.b;
    }
    
    // 合并不等式约束
    if (D.rows() > 0 && other.D.rows() > 0) {
        result.D.resize(D.rows() + other.D.rows(), std::max(D.cols(), other.D.cols()));
        result.f.resize(D.rows() + other.D.rows());
        
        result.D.topRows(D.rows()) = D;
        result.D.bottomRows(other.D.rows()) = other.D;
        result.f.head(D.rows()) = f;
        result.f.tail(other.D.rows()) = other.f;
    } else if (D.rows() > 0) {
        result.D = D;
        result.f = f;
    } else if (other.D.rows() > 0) {
        result.D = other.D;
        result.f = other.f;
    }
    
    result.weight = weight;
    return result;
}

WbcTask WbcTask::operator*(double weight_factor) const {
    WbcTask result = *this;
    result.weight = weight * weight_factor;
    return result;
}

WbcBase::WbcBase(std::shared_ptr<QuadrupedRobot> robot_model)
    : robot_model_(robot_model) {
    
    // 初始化状态向量
    current_rpy_.setZero();
    current_pos_.setZero();
    current_omega_.setZero();
    current_vel_.setZero();
    current_joint_pos_.setZero();
    current_joint_vel_.setZero();
    current_contact_states_.setZero();
    
    // 初始化动力学矩阵
    mass_matrix_.resize(18, 18);  // 6 floating base + 12 joints
    nonlinear_effects_.resize(18);
    
    for (int i = 0; i < 4; ++i) {
        jacobians_[i].setZero();
    }
}

void WbcBase::updateRobotState(const Vec13& measured_state, const VecInt4& contact_states) {
    // 安全检查
    if (!robot_model_) {
        std::cerr << "[WbcBase] Error: robot_model_ is null!" << std::endl;
        return;
    }

    // 提取状态信息
    current_rpy_ = measured_state.segment<3>(0);
    current_pos_ = measured_state.segment<3>(3);
    current_omega_ = measured_state.segment<3>(6);
    current_vel_ = measured_state.segment<3>(9);
    current_contact_states_ = contact_states;

    // 获取关节状态 - 添加异常处理
    try {
        current_joint_pos_ = robot_model_->getJointPositions();
        current_joint_vel_ = robot_model_->getJointVelocities();
    } catch (const std::exception& e) {
        std::cerr << "[WbcBase] Exception in getting joint states: " << e.what() << std::endl;
        current_joint_pos_.setZero();
        current_joint_vel_.setZero();
        return;
    }

    // 更新动力学信息
    updateDynamics();
    updateJacobians();
}

void WbcBase::updateDynamics() {
    // 使用机器人模型计算质量矩阵和非线性项
    // 这里简化实现，实际应该调用机器人动力学库
    
    // 质量矩阵 (18x18: 6 floating base + 12 joints)
    mass_matrix_.setIdentity();
    mass_matrix_.topLeftCorner<6, 6>() *= 15.0;  // 机器人质量约15kg
    mass_matrix_.bottomRightCorner<12, 12>() *= 0.1;  // 关节惯量
    
    // 非线性项 (重力 + 科里奥利力)
    nonlinear_effects_.setZero();
    nonlinear_effects_(5) = -9.81 * 15.0;  // 重力项
}

void WbcBase::updateJacobians() {
    if (!robot_model_) {
        std::cerr << "[WbcBase] Error: robot_model_ is null in updateJacobians!" << std::endl;
        return;
    }

    try {
        for (int leg = 0; leg < 4; ++leg) {
            KDL::Jacobian kdl_jacobian = robot_model_->getJacobian(leg);

            // 安全检查
            if (kdl_jacobian.columns() == 0) {
                std::cerr << "[WbcBase] Warning: Jacobian for leg " << leg << " has 0 columns" << std::endl;
                jacobians_[leg].setIdentity();  // 设置为单位矩阵
                continue;
            }

            // 正确映射：使用原始指针 + 显式类型转换
            jacobians_[leg] = Eigen::Map<Eigen::MatrixXd>(
                kdl_jacobian.data.data(),  // 获取底层数据指针
                3,                         // 行数 (位置雅可比)
                static_cast<int>(kdl_jacobian.columns())  // 显式转换列数为 int
            );
        }
    } catch (const std::exception& e) {
        std::cerr << "[WbcBase] Exception in updateJacobians: " << e.what() << std::endl;
        // 设置为单位矩阵作为备用
        for (int leg = 0; leg < 4; ++leg) {
            jacobians_[leg].setIdentity();
        }
    }
}

WbcTask WbcBase::formulateFloatingBaseDynamics() {
    // 浮动基座动力学约束: M * qdd + h = S^T * tau + J^T * F
    // 重新排列: M * qdd - J^T * F - S^T * tau = -h
    
    Mat A(18, decision_var_dim_);
    A.setZero();
    
    // qdd项 (浮动基座加速度)
    A.block<18, 6>(0, 0) = mass_matrix_.leftCols<6>();
    
    // F项 (接触力)
    for (int leg = 0; leg < 4; ++leg) {
        if (current_contact_states_(leg) > 0) {
            A.block<3, 3>(6 + 3*leg, floating_base_dim_ + 3*leg) = -jacobians_[leg].transpose();
        }
    }
    
    // tau项 (关节力矩)
    Mat S = Mat::Zero(18, 12);
    S.bottomRightCorner<12, 12>().setIdentity();  // 选择矩阵
    A.block<18, 12>(0, floating_base_dim_ + contact_force_dim_) = -S;
    
    Vec b = -nonlinear_effects_;
    
    return WbcTask(A, b);
}

WbcTask WbcBase::formulateTorqueLimits() {
    // 关节力矩限制: -tau_max <= tau <= tau_max
    // 转换为不等式约束: tau <= tau_max 和 -tau <= tau_max
    
    Mat D(24, decision_var_dim_);  // 12*2 = 24 个不等式约束
    D.setZero();
    
    // tau <= tau_max
    D.block<12, 12>(0, floating_base_dim_ + contact_force_dim_) = Mat::Identity(12, 12);
    
    // -tau <= tau_max (即 tau >= -tau_max)
    D.block<12, 12>(12, floating_base_dim_ + contact_force_dim_) = -Mat::Identity(12, 12);
    
    Vec f(24);
    for (int i = 0; i < 4; ++i) {
        f.segment<3>(i*3) = torque_limits_;
        f.segment<3>(12 + i*3) = torque_limits_;
    }
    
    return WbcTask(Mat(), Vec(), D, f);
}

WbcTask WbcBase::formulateFrictionCone(const VecInt4& contact_states) {
    int num_contacts = contact_states.sum();
    int num_swing = 4 - num_contacts;
    
    // 摩擦锥约束矩阵 (5个约束每个接触足)
    Mat friction_pyramid(5, 3);
    friction_pyramid << 0, 0, -1,                    // F_z >= 0
                        1, 0, -friction_coeff_,      // |F_x| <= mu * F_z
                        -1, 0, -friction_coeff_,
                        0, 1, -friction_coeff_,      // |F_y| <= mu * F_z
                        0, -1, -friction_coeff_;
    
    // 非接触足约束 (3个约束每个摆动足)
    Mat A_swing(3 * num_swing, decision_var_dim_);
    Vec b_swing(3 * num_swing);
    A_swing.setZero();
    b_swing.setZero();
    
    // 接触足摩擦锥约束
    Mat D_contact(5 * num_contacts, decision_var_dim_);
    Vec f_contact(5 * num_contacts);
    D_contact.setZero();
    f_contact.setZero();
    
    int swing_idx = 0, contact_idx = 0;
    for (int leg = 0; leg < 4; ++leg) {
        if (contact_states(leg) > 0) {
            // 接触足: 摩擦锥约束
            D_contact.block<5, 3>(5 * contact_idx, floating_base_dim_ + 3 * leg) = friction_pyramid;
            contact_idx++;
        } else {
            // 摆动足: 接触力为零
            A_swing.block<3, 3>(3 * swing_idx, floating_base_dim_ + 3 * leg) = Mat::Identity(3, 3);
            swing_idx++;
        }
    }
    
    return WbcTask(A_swing, b_swing, D_contact, f_contact);
}

WbcTask WbcBase::formulateNoContactMotion(const VecInt4& contact_states) {
    // 接触足的运动约束: J * qdd + Jdot * qd = 0
    int num_contacts = contact_states.sum();
    
    Mat A(3 * num_contacts, decision_var_dim_);
    Vec b(3 * num_contacts);
    A.setZero();
    b.setZero();
    
    int contact_idx = 0;
    for (int leg = 0; leg < 4; ++leg) {
        if (contact_states(leg) > 0) {
            // 接触约束: J * qdd = -Jdot * qd
            A.block<3, 6>(3 * contact_idx, 0) = jacobians_[leg];
            A.block<3, 12>(3 * contact_idx, 6) = jacobians_[leg].rightCols<12>();
            
            // 简化: 假设 Jdot * qd = 0
            b.segment<3>(3 * contact_idx).setZero();
            contact_idx++;
        }
    }
    
    return WbcTask(A, b);
}

WbcTask WbcBase::formulateBodyAccelTask(const Vec13& desired_state,
                                        const Vec13& measured_state,
                                        double dt) {
    // 身体加速度任务: 跟踪期望的线性和角加速度
    Mat A(6, decision_var_dim_);
    A.setZero();
    A.block<6, 6>(0, 0) = Mat::Identity(6, 6);  // 直接控制浮动基座加速度

    // 计算期望加速度 (简化PD控制)
    Vec3 pos_error = desired_state.segment<3>(3) - measured_state.segment<3>(3);
    Vec3 vel_error = desired_state.segment<3>(9) - measured_state.segment<3>(9);
    Vec3 desired_linear_accel = 100.0 * pos_error + 20.0 * vel_error;  // PD控制

    Vec3 rpy_error = desired_state.segment<3>(0) - measured_state.segment<3>(0);
    Vec3 omega_error = desired_state.segment<3>(6) - measured_state.segment<3>(6);
    Vec3 desired_angular_accel = 50.0 * rpy_error + 10.0 * omega_error;  // PD控制

    Vec b(6);
    b.head<3>() = desired_linear_accel;
    b.tail<3>() = desired_angular_accel;

    return WbcTask(A, b, weight_body_accel_);
}

WbcTask WbcBase::formulateSwingLegTask(const Vec34& desired_foot_pos,
                                       const Vec34& desired_foot_vel,
                                       const VecInt4& contact_states) {
    // 摆动腿任务: 跟踪期望的足端位置和速度
    int num_swing = 4 - contact_states.sum();

    Mat A(3 * num_swing, decision_var_dim_);
    Vec b(3 * num_swing);
    A.setZero();
    b.setZero();

    int swing_idx = 0;
    for (int leg = 0; leg < 4; ++leg) {
        if (contact_states(leg) == 0) {
            // 摆动腿: J * qdd = desired_foot_accel - Jdot * qd
            A.block<3, 6>(3 * swing_idx, 0) = jacobians_[leg];
            A.block<3, 12>(3 * swing_idx, 6) = jacobians_[leg].rightCols<12>();

            // 计算期望足端加速度 (简化PD控制)
            Vec3 current_foot_pos = robot_model_->getFootPosition(leg);
            Vec3 current_foot_vel = robot_model_->getFootVelocity(leg);

            Vec3 pos_error = desired_foot_pos.col(leg) - current_foot_pos;
            Vec3 vel_error = desired_foot_vel.col(leg) - current_foot_vel;
            Vec3 desired_foot_accel = 200.0 * pos_error + 40.0 * vel_error;

            b.segment<3>(3 * swing_idx) = desired_foot_accel;
            swing_idx++;
        }
    }

    return WbcTask(A, b, weight_swing_leg_);
}

WbcTask WbcBase::formulateContactForceTask(const Vec12& desired_forces,
                                           const VecInt4& contact_states) {
    // 接触力任务: 跟踪MPC输出的期望接触力
    Mat A(12, decision_var_dim_);
    A.setZero();
    A.block<12, 12>(0, floating_base_dim_) = Mat::Identity(12, 12);

    Vec b = desired_forces;

    // 对于非接触足，期望接触力为零
    for (int leg = 0; leg < 4; ++leg) {
        if (contact_states(leg) == 0) {
            b.segment<3>(3 * leg).setZero();
        }
    }

    return WbcTask(A, b, weight_contact_force_);
}

void WbcBase::extractJointCommands(const Vec& wbc_solution,
                                   Vec12& joint_positions,
                                   Vec12& joint_velocities,
                                   Vec12& joint_torques,
                                   double dt) {
    // 从WBC解中提取信息
    Vec6 floating_base_accel = wbc_solution.segment<6>(0);
    Vec12 contact_forces = wbc_solution.segment<12>(floating_base_dim_);
    joint_torques = wbc_solution.segment<12>(floating_base_dim_ + contact_force_dim_);

    // 积分得到关节位置和速度 (简化实现)
    // 注意：这里简化了关节加速度的计算，实际应该从动力学模型中获取
    Vec12 joint_accel = Vec12::Zero();  // 简化假设关节加速度为零
    joint_velocities = current_joint_vel_ + joint_accel * dt;
    joint_positions = current_joint_pos_ + joint_velocities * dt;

    // 限制关节位置到安全范围
    for (int i = 0; i < 12; ++i) {
        joint_positions(i) = std::max(-M_PI, std::min(M_PI, joint_positions(i)));
        joint_velocities(i) = std::max(-21.0, std::min(21.0, joint_velocities(i)));
    }
}

} // namespace mit_controller
