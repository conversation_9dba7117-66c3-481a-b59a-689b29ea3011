//
// Created by user on 2024-12-19.
//

#include "mit_controller/safety/SafetyChecker.h"

namespace mit_controller {

SafetyChecker::SafetyChecker(double max_roll_angle,
                             double max_pitch_angle,
                             double max_joint_torque,
                             double max_joint_velocity,
                             double max_foot_position)
    : max_roll_angle_(max_roll_angle),
      max_pitch_angle_(max_pitch_angle),
      max_joint_torque_(max_joint_torque),
      max_joint_velocity_(max_joint_velocity),
      max_foot_position_(max_foot_position) {
}

bool SafetyChecker::checkOrientation(const Vec3& rpy) const {
    // 检查roll角度
    if (std::abs(rpy(0)) > max_roll_angle_) {
        std::cerr << "[SafetyChecker] Roll angle safety check failed! "
                  << "Current: " << rpy(0) * 180.0 / M_PI << " deg, "
                  << "Max: " << max_roll_angle_ * 180.0 / M_PI << " deg" << std::endl;
        return false;
    }

    // 检查pitch角度
    if (std::abs(rpy(1)) > max_pitch_angle_) {
        std::cerr << "[SafetyChe<PERSON>] Pitch angle safety check failed! "
                  << "Current: " << rpy(1) * 180.0 / M_PI << " deg, "
                  << "Max: " << max_pitch_angle_ * 180.0 / M_PI << " deg" << std::endl;
        return false;
    }

    return true;
}

bool SafetyChecker::checkJointTorques(const Vec12& joint_torques) const {
    for (int i = 0; i < 12; ++i) {
        if (std::abs(joint_torques(i)) > max_joint_torque_) {
            std::cerr << "[SafetyChecker] Joint torque safety check failed! "
                      << "Joint " << i << ": " << joint_torques(i) << " Nm, "
                      << "Max: " << max_joint_torque_ << " Nm" << std::endl;
            return false;
        }
    }
    return true;
}

bool SafetyChecker::checkJointVelocities(const Vec12& joint_velocities) const {
    for (int i = 0; i < 12; ++i) {
        if (std::abs(joint_velocities(i)) > max_joint_velocity_) {
            std::cerr << "[SafetyChecker] Joint velocity safety check failed! "
                      << "Joint " << i << ": " << joint_velocities(i) << " rad/s, "
                      << "Max: " << max_joint_velocity_ << " rad/s" << std::endl;
            return false;
        }
    }
    return true;
}

bool SafetyChecker::checkFootPositions(const Vec34& foot_positions) const {
    for (int leg = 0; leg < 4; ++leg) {
        // 检查X方向
        if (std::abs(foot_positions(0, leg)) > max_foot_position_) {
            std::cerr << "[SafetyChecker] Foot position X safety check failed! "
                      << "Leg " << leg << ": " << foot_positions(0, leg) << " m, "
                      << "Max: " << max_foot_position_ << " m" << std::endl;
            return false;
        }

        // 检查Y方向
        if (std::abs(foot_positions(1, leg)) > max_foot_position_) {
            std::cerr << "[SafetyChecker] Foot position Y safety check failed! "
                      << "Leg " << leg << ": " << foot_positions(1, leg) << " m, "
                      << "Max: " << max_foot_position_ << " m" << std::endl;
            return false;
        }

        // 检查Z方向（足端不能高于身体）
        if (foot_positions(2, leg) > 0.0) {
            std::cerr << "[SafetyChecker] Foot position Z safety check failed! "
                      << "Leg " << leg << " is above body: " << foot_positions(2, leg) << " m" << std::endl;
            return false;
        }

        // 检查足端不能太低（超出腿长）
        if (foot_positions(2, leg) < -0.4) {  // Go2最大腿长约0.4m
            std::cerr << "[SafetyChecker] Foot position Z safety check failed! "
                      << "Leg " << leg << " is too low: " << foot_positions(2, leg) << " m" << std::endl;
            return false;
        }
    }
    return true;
}

bool SafetyChecker::checkBodyHeight(double body_height, double min_height, double max_height) const {
    if (body_height < min_height) {
        std::cerr << "[SafetyChecker] Body height too low! "
                  << "Current: " << body_height << " m, Min: " << min_height << " m" << std::endl;
        return false;
    }

    if (body_height > max_height) {
        std::cerr << "[SafetyChecker] Body height too high! "
                  << "Current: " << body_height << " m, Max: " << max_height << " m" << std::endl;
        return false;
    }

    return true;
}

bool SafetyChecker::checkOverall(const Vec3& rpy,
                                 const Vec12& joint_torques,
                                 const Vec12& joint_velocities,
                                 const Vec34& foot_positions,
                                 double body_height) const {
    bool safe = true;

    safe &= checkOrientation(rpy);
    safe &= checkJointTorques(joint_torques);
    safe &= checkJointVelocities(joint_velocities);
    safe &= checkFootPositions(foot_positions);
    safe &= checkBodyHeight(body_height);

    return safe;
}

void SafetyChecker::saturateJointTorques(Vec12& joint_torques) const {
    for (int i = 0; i < 12; ++i) {
        joint_torques(i) = saturate(joint_torques(i), -max_joint_torque_, max_joint_torque_);
    }
}

void SafetyChecker::saturateFootPositions(Vec34& foot_positions) const {
    for (int leg = 0; leg < 4; ++leg) {
        // 限制X和Y方向
        foot_positions(0, leg) = saturate(foot_positions(0, leg), -max_foot_position_, max_foot_position_);
        foot_positions(1, leg) = saturate(foot_positions(1, leg), -max_foot_position_, max_foot_position_);
        
        // 限制Z方向（不能高于身体，不能低于最大腿长）
        foot_positions(2, leg) = saturate(foot_positions(2, leg), -0.4, 0.0);
    }
}

} // namespace mit_controller
