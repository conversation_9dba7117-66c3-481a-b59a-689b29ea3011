//
// Created by tlab-uav on 24-9-18.
//

#include "mit_controller/FSM/StateTrotting.h"

#include <mit_controller/common/mathTools.h>


StateTrotting::StateTrotting(CtrlComponent &ctrlComp) : FSMState(FSMStateName::TROTTING, "trotting", ctrlComp),
                                                        estimator_(ctrlComp.estimator_),// CtrlComponent 对象中的 estimator_ 成员（也是一个智能指针）赋值给 StateTrotting 类中的 estimator_ 成员
                                                        robot_model_(ctrlComp.robot_model_),
                                                        balance_ctrl_(ctrlComp.balance_ctrl_),
                                                        convex_mpc_ctrl_(ctrlComp.convex_mpc_ctrl_),
                                                        wave_generator_(ctrl_comp_.wave_generator_),
                                                        gait_generator_(ctrlComp) {
    gait_height_ = 0.08;
    Kpp = Vec3(70, 70, 70).asDiagonal();
    Kdp = Vec3(10, 10, 10).asDiagonal();
    kp_w_ = 780;
    Kd_w_ = Vec3(70, 70, 70).asDiagonal();
    Kp_swing_ = Vec3(400, 400, 400).asDiagonal();
    Kd_swing_ = Vec3(10, 10, 10).asDiagonal();

    v_x_limit_ << -0.4, 0.4;
    v_y_limit_ << -0.3, 0.3;
    w_yaw_limit_ << -0.5, 0.5;
    dt_ = 1.0 / ctrl_comp_.frequency_;

    // 初始化安全检查器
    safety_checker_ = std::make_shared<SafetyChecker>(
        M_PI_4,    // max_roll_angle (45度)
        M_PI_4,    // max_pitch_angle (45度)
        33.5,      // max_joint_torque (Go2电机最大力矩)
        21.0,      // max_joint_velocity (Go2电机最大速度)
        0.4        // max_foot_position
    );

    // WBC控制器将在第一次使用时初始化
    wbc_controller_ = nullptr;

    // 可以通过参数控制是否启用WBC
    use_wbc_ = false;  // 默认不启用，可以通过参数或手柄切换
}

void StateTrotting::enter() {
     // 初始化操作
    pcd_ = estimator_->getPosition();   // 期望位置先设为当前位置
    printf("pcd(2)in enter: %.4f\n",  pcd_(2));
    pcd_(2) = -estimator_->getFeetPos2Body()(2, 0); //  ？？
    printf("pcd(2)in enter change: %.4f\n",  pcd_(2));
    v_cmd_body_.setZero();              // 期望速度设为0
    yaw_cmd_ = estimator_->getYaw();    // 期望角度设为当前角度
    Rd = rotz(yaw_cmd_);                // 期望角度旋转矩阵
    w_cmd_global_.setZero();            // 期望角速度设为0

    ctrl_comp_.control_inputs_.command = 0; // control_input_msgs, commond为0，表示invalid
    gait_generator_.restart();  // 还没仔细看其实现，------------
}

void StateTrotting::run() {
    // 添加计数器变量
    // static int counter = 0;

    pos_body_ = estimator_->getPosition();  // Body Position
    vel_body_ = estimator_->getVelocity();  // Body Velocity

    B2G_RotMat = estimator_->getRotation(); // Body to Global Rotation Matrix 身体相对全局的旋转矩阵
    G2B_RotMat = B2G_RotMat.transpose();
    // rpy
    rpy_ = estimator_->getrpy(); // 获取当前姿态角
    R_yaw = rotz(rpy_(2)); // 获取当前yaw角旋转矩阵
    w_body_ = estimator_->getGyroGlobal(); // 获取当前角速度
    // x0_ = Vec13(pc_, rpy_, d_pc, d_wb,g);  初始状态向量
    x0_ << rpy_, pos_body_, w_body_, vel_body_, -9.81;

    // 安全检查 - 检查当前状态
    if (!safety_checker_->checkOrientation(rpy_)) {
        std::cerr << "[StateTrotting] Orientation safety check failed, switching to passive mode!" << std::endl;
        return; // 或者切换到安全状态
    }

    if (!safety_checker_->checkBodyHeight(pos_body_(2))) {
        std::cerr << "[StateTrotting] Body height safety check failed!" << std::endl;
        return;
    }
    
    // 每20次循环输出一次高度信息
    // if (++counter % 20 == 0) {
    //     printf("pos_body_(2): %.4f, pcd(2): %.4f\n", pos_body_(2), pcd_(2));
    // }

    getUserCmd();  // 获取用户输入的期望速度v_cmd_body_和期望角速度d_yaw_cmd_，《局部坐标系下表示》； 缺陷：只能改变yaw角度，且不能改变高度
    calcCmd();     // 计算期望的速度vel_target_，期望位置pcd_, 和期望的角度w_cmd_global_《全局坐标系下表示》

    gait_generator_.setGait(vel_target_.segment(0, 2), w_cmd_global_(2), gait_height_); // 设置步态参数，包括速度、角速度、步态高度
    gait_generator_.generate(pos_feet_global_goal_, vel_feet_global_goal_);             // 生成期望的足部位置pos_feet_global_goal_和速度vel_feet_global_goal_

    if (use_wbc_) {
        // 使用WBC控制
        calcTauWithWBC();
    } else {
        // 使用原始MPC直接控制
        calcTau(); // 计算期望的力矩， 这里要替换成MPC控制
        calcQQd(); // 根据足端期望位置和速度计算期望的关节角度q和角速度qd，用于伺服电机控制
    }


    // 判断是否需要摆动腿，根据给定的期望速度进行判断
    if (checkStepOrNot()) {
        wave_generator_->status_ = WaveStatus::WAVE_ALL;    // 摆动所有腿，根据不太参数交替触底，腾空
    } else {
        wave_generator_->status_ = WaveStatus::STANCE_ALL; // 站立所有腿，四条腿始终接触地面
    }

    calcGain(); // 用于伺服电机控制
}

void StateTrotting::exit() {
    wave_generator_->status_ = WaveStatus::SWING_ALL;
}

FSMStateName StateTrotting::checkChange() {
    switch (ctrl_comp_.control_inputs_.command) {
        case 1:
            return FSMStateName::PASSIVE;
        case 2:
            return FSMStateName::FIXEDSTAND;
        default:
            return FSMStateName::TROTTING;
    }
}

void StateTrotting::getUserCmd() {
    /* Movement */
    v_cmd_body_(0) = invNormalize(ctrl_comp_.control_inputs_.ly, v_x_limit_(0), v_x_limit_(1));
    v_cmd_body_(1) = -invNormalize(ctrl_comp_.control_inputs_.lx, v_y_limit_(0), v_y_limit_(1));
    v_cmd_body_(2) = 0;

    /* Turning */
    d_yaw_cmd_ = -invNormalize(ctrl_comp_.control_inputs_.rx, w_yaw_limit_(0), w_yaw_limit_(1));
    d_yaw_cmd_ = 0.9 * d_yaw_cmd_past_ + (1 - 0.9) * d_yaw_cmd_;
    d_yaw_cmd_past_ = d_yaw_cmd_;

    /* WBC切换 - 使用右摇杆垂直方向切换 */
    static bool wbc_switch_pressed = false;
    if (ctrl_comp_.control_inputs_.ry > 0.8 && !wbc_switch_pressed) {
        use_wbc_ = !use_wbc_;
        wbc_switch_pressed = true;
        std::cout << "[StateTrotting] WBC " << (use_wbc_ ? "ENABLED" : "DISABLED") << std::endl;
    } else if (ctrl_comp_.control_inputs_.ry < 0.5) {
        wbc_switch_pressed = false;
    }
}

void StateTrotting::calcCmd() {
    /* Movement */
    vel_target_ = B2G_RotMat * v_cmd_body_;

    // 限制速度，位置
    vel_target_(0) =
            saturation(vel_target_(0), Vec2(vel_body_(0) - 0.2, vel_body_(0) + 0.2));
    vel_target_(1) =
            saturation(vel_target_(1), Vec2(vel_body_(1) - 0.2, vel_body_(1) + 0.2));

    pcd_(0) = saturation(pcd_(0) + vel_target_(0) * dt_,
                         Vec2(pos_body_(0) - 0.05, pos_body_(0) + 0.05));
    pcd_(1) = saturation(pcd_(1) + vel_target_(1) * dt_,
                         Vec2(pos_body_(1) - 0.05, pos_body_(1) + 0.05));

    vel_target_(2) = 0;

    /* Turning */
    yaw_cmd_ = yaw_cmd_ + d_yaw_cmd_ * dt_;
    Rd = rotz(yaw_cmd_);
    w_cmd_global_(2) = d_yaw_cmd_;

    
}

void StateTrotting::calcTau() {
    // body位置误差
    pos_error_ = pcd_ - pos_body_;
    // body速度误差
    vel_error_ = vel_target_ - vel_body_;

    //  PD控制率，计算期望的body加速度, 和期望的角加速度
    /*
        [ddpc_d] = [Kpp*(pcd-pc)+Kdp*(vc-vc_d)]
        [dwb_d ] = [Kpw*log(Rd*R^T)+Kdw*(wbd-w)]  --- (2)
    */
    Vec3 dd_pcd = Kpp * pos_error_ + Kdp * vel_error_;
    Vec3 d_wbd = kp_w_ * rotMatToExp(Rd * G2B_RotMat) +
                 Kd_w_ * (w_cmd_global_ - estimator_->getGyroGlobal());


    // 约束摆动腿的加速度范围，不能太大
    dd_pcd(0) = saturation(dd_pcd(0), Vec2(-3, 3));
    dd_pcd(1) = saturation(dd_pcd(1), Vec2(-3, 3));
    dd_pcd(2) = saturation(dd_pcd(2), Vec2(-5, 5));

    d_wbd(0) = saturation(d_wbd(0), Vec2(-40, 40));
    d_wbd(1) = saturation(d_wbd(1), Vec2(-40, 40));
    d_wbd(2) = saturation(d_wbd(2), Vec2(-10, 10));

    // 计算estimated的四条足端相对于body的位置in world frame
    const Vec34 pos_feet_body_global = estimator_->getFeetPos2Body();
    // // 平衡控制器Balance Controller, 计算期望的足端力F*, in global frame
    // Vec34 force_feet_global2 =
    //         -balance_ctrl_->calF(dd_pcd, d_wbd, B2G_RotMat, pos_feet_body_global, wave_generator_->contact_);
    // std::cout << "force_feet_global2: " << force_feet_global2 << std::endl;
    //  MPC控制器
    Vec34 force_feet_global =
            -convex_mpc_ctrl_->calF(x0_,vel_target_, w_cmd_global_, R_yaw, B2G_RotMat, pos_feet_body_global, wave_generator_->contact_, dt_, pcd_);
    // std::cout << "force_feet_global: " << force_feet_global << std::endl;

    // Get the estimated feet position&velocity in world frame
    Vec34 pos_feet_global = estimator_->getFeetPos();
    Vec34 vel_feet_global = estimator_->getFeetVel();

    // Swing Leg Control, 计算摆动腿的力，global frame
    for (int i(0); i < 4; ++i) {
        if (wave_generator_->contact_(i) == 0) { // swing foot
            force_feet_global.col(i) =
                Kp_swing_ * (pos_feet_global_goal_.col(i) - pos_feet_global.col(i)) +
                Kd_swing_ * (vel_feet_global_goal_.col(i) - vel_feet_global.col(i));
        }
    }

    // 将期望的足端力转换到body frame
    Vec34 force_feet_body_ = G2B_RotMat * force_feet_global;

    
    // 安全检查 - 检查输出力矩
    Vec12 joint_torques;
    for (int i = 0; i < 4; i++) {
        KDL::JntArray torque = robot_model_->getTorque(force_feet_body_.col(i), i);
        for (int j = 0; j < 3; j++) {
            joint_torques(i * 3 + j) = torque(j);
        }
    }

    // 力矩安全检查和饱和
    if (!safety_checker_->checkJointTorques(joint_torques)) {
        std::cerr << "[StateTrotting] Joint torque safety check failed, saturating torques!" << std::endl;
        safety_checker_->saturateJointTorques(joint_torques);
    }

    // 设置安全的力矩命令
    for (int i = 0; i < 4; i++) {
        for (int j = 0; j < 3; j++) {
            bool success = ctrl_comp_.joint_torque_command_interface_[i * 3 + j].get().set_value(joint_torques(i * 3 + j));
            if (!success) {
                std::cerr << "Error: Failed to set torque for joint " << i * 3 + j << std::endl;
            }
        }
    }

}

void StateTrotting::calcTauWithWBC() {
    // 1. 准备WBC输入数据
    Vec13 desired_state = x0_;  // 当前状态作为期望状态的基础

    // 2. 从MPC获取期望接触力
    Vec34 force_feet_global = -convex_mpc_ctrl_->calF(x0_, vel_target_, w_cmd_global_,
                                                      R_yaw, B2G_RotMat,
                                                      estimator_->getFeetPos2Body(),
                                                      wave_generator_->contact_, dt_);

    // 转换为12维向量
    Vec12 desired_forces;
    for (int i = 0; i < 4; ++i) {
        desired_forces.segment<3>(i * 3) = force_feet_global.col(i);
    }

    // 3. 设置期望足端轨迹
    wbc_controller_->setDesiredFootTrajectories(pos_feet_global_goal_, vel_feet_global_goal_);

    // 4. 调用WBC求解
    Vec wbc_solution = wbc_controller_->update(desired_state, desired_forces, x0_,
                                               wave_generator_->contact_, dt_);

    // 5. 提取关节控制命令
    Vec12 joint_positions, joint_velocities, joint_torques;
    wbc_controller_->extractJointCommands(wbc_solution, joint_positions, joint_velocities, joint_torques, dt_);

    // 6. 安全检查
    if (!safety_checker_->checkJointTorques(joint_torques)) {
        std::cerr << "[StateTrotting] WBC joint torque safety check failed, saturating torques!" << std::endl;
        safety_checker_->saturateJointTorques(joint_torques);
    }

    // 7. 设置控制命令
    for (int i = 0; i < 4; i++) {
        for (int j = 0; j < 3; j++) {
            int joint_idx = i * 3 + j;

            // 设置力矩命令
            bool success = ctrl_comp_.joint_torque_command_interface_[joint_idx].get().set_value(joint_torques(joint_idx));
            if (!success) {
                std::cerr << "Error: Failed to set torque for joint " << joint_idx << std::endl;
            }

            // 设置位置和速度命令（如果需要）
            ctrl_comp_.joint_position_command_interface_[joint_idx].get().set_value(joint_positions(joint_idx));
            ctrl_comp_.joint_velocity_command_interface_[joint_idx].get().set_value(joint_velocities(joint_idx));
        }
    }
}

void StateTrotting::calcQQd() {
    // 足端相对于身体的位姿p，M
    const std::vector<KDL::Frame> pos_feet_body = robot_model_->getFeet2BPositions();

    // 足端的目标位置、速度（全局坐标系）
    Vec34 pos_feet_target, vel_feet_target;
    // 表示到body坐标系，足端相对于身体的位置、速度
    for (int i(0); i < 4; ++i) {
        pos_feet_target.col(i) = G2B_RotMat * (pos_feet_global_goal_.col(i) - pos_body_);
        vel_feet_target.col(i) = G2B_RotMat * (vel_feet_global_goal_.col(i) - vel_body_);
    }

    // 根据足端位置、速度，计算期望的关节位置、速度
    Vec12 q_goal = robot_model_->getQ(pos_feet_target);
    Vec12 qd_goal = robot_model_->getQd(pos_feet_body, vel_feet_target);
    for (int i = 0; i < 12; i++) {
        ctrl_comp_.joint_position_command_interface_[i].get().set_value(q_goal(i)); // 设置期望的关节位置
        ctrl_comp_.joint_velocity_command_interface_[i].get().set_value(qd_goal(i));// 设置期望的关节速度
    }
}

void StateTrotting::calcGain() const {
    for (int i(0); i < 4; ++i) {
        if (wave_generator_->contact_(i) == 0) {
            // swing gain
            for (int j = 0; j < 3; j++) {
                ctrl_comp_.joint_kp_command_interface_[i * 3 + j].get().set_value(3.0); // 设置关节的kp=3
                ctrl_comp_.joint_kd_command_interface_[i * 3 + j].get().set_value(2.0); // 设置关节的kd=2
            }
        } else {
            // contact gain
            for (int j = 0; j < 3; j++) {
                ctrl_comp_.joint_kp_command_interface_[i * 3 + j].get().set_value(0.8); // 设置关节的kp=0.8
                ctrl_comp_.joint_kd_command_interface_[i * 3 + j].get().set_value(0.8); // 设置关节的kd=0.8
            }
        }
    }
}

// check if the robot is in the state of stepping
bool StateTrotting::checkStepOrNot() {
    // 满足以下4个任意条件之一，则摆动腿. 1. 期望速度不为0；2. 期望位置误差不为0；3. 期望速度误差不为0；4. 期望角速度不为0
    if (fabs(v_cmd_body_(0)) > 0.03 || fabs(v_cmd_body_(1)) > 0.03 ||
        fabs(pos_error_(0)) > 0.08 || fabs(pos_error_(1)) > 0.08 ||
        fabs(vel_error_(0)) > 0.05 || fabs(vel_error_(1)) > 0.05 ||
        fabs(d_yaw_cmd_) > 0.20) {
        return true;
    }
    return false;
}