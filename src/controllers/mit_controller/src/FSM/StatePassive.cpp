//
// Created by tlab-uav on 24-9-6.
//

#include "mit_controller/FSM/StatePassive.h"

#include <iostream>
#include <utility>

StatePassive::StatePassive(CtrlComponent &ctrlComp) : FSMState(
    FSMStateName::PASSIVE, "passive", ctrlComp) {
}

void StatePassive::enter() {
    // 设置关节扭矩
    for (auto& i : ctrl_comp_.joint_torque_command_interface_) {
        bool success = i.get().set_value(0.0);
        if (!success) {
            std::cerr << "Failed to set joint torque command" << std::endl;
        }
    }

    // 设置关节位置
    for (auto& i : ctrl_comp_.joint_position_command_interface_) {
        bool success = i.get().set_value(0.0);
        if (!success) {
            std::cerr << "Failed to set joint position command" << std::endl;
        }
    }

    // 设置关节速度
    for (auto& i : ctrl_comp_.joint_velocity_command_interface_) {
        bool success = i.get().set_value(0.0);
        if (!success) {
            std::cerr << "Failed to set joint velocity command" << std::endl;
        }
    }

    // 设置关节比例增益
    for (auto& i : ctrl_comp_.joint_kp_command_interface_) {
        bool success = i.get().set_value(0.0);
        if (!success) {
            std::cerr << "Failed to set joint kp command" << std::endl;
        }
    }

    // 设置关节微分增益
    for (auto& i : ctrl_comp_.joint_kd_command_interface_) {
        bool success = i.get().set_value(1.0);
        if (!success) {
            std::cerr << "Failed to set joint kd command" << std::endl;
        }
    }

    // 设置控制输入命令
    ctrl_comp_.control_inputs_.command = 0;
}

void StatePassive::run() {
}

void StatePassive::exit() {
}

FSMStateName StatePassive::checkChange() {
    if (ctrl_comp_.control_inputs_.command == 2) {
        return FSMStateName::FIXEDDOWN;
    }
    return FSMStateName::PASSIVE;
}