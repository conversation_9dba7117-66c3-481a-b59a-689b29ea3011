//
// Created by tlab-uav on 24-9-13.
//

#include "mit_controller/FSM/StateFreeStand.h"
#include "mit_controller/common/mathTools.h"

StateFreeStand::StateFreeStand(CtrlComponent &ctrl_component) : FSMState(FSMStateName::FREESTAND, "free stand",
                                                                         ctrl_component),
                                                                robot_model_(ctrl_component.robot_model_) {
    row_max_ = 20 * M_PI / 180;
    row_min_ = -row_max_;
    pitch_max_ = 15 * M_PI / 180;
    pitch_min_ = -pitch_max_;
    yaw_max_ = 20 * M_PI / 180;
    yaw_min_ = -yaw_max_;
    height_max_ = 0.1;
    height_min_ = -height_max_;
}

void StateFreeStand::enter() {
    for (int i = 0; i < 12; i++) {
        // 设置关节的kp和kd
        bool success = ctrl_comp_.joint_kp_command_interface_[i].get().set_value(100.0); // kp = 100
        if (!success) {
            std::cerr << "Failed to set joint kp for joint " << i << std::endl;
        }

        success = ctrl_comp_.joint_kd_command_interface_[i].get().set_value(5.0);   // kd = 5
        if (!success) {
            std::cerr << "Failed to set joint kd for joint " << i << std::endl;
        }
    }

    // 获取当前的关节位置和足部位置
    init_joint_pos_ = robot_model_->current_joint_pos_;    // 当前关节角度
    init_foot_pos_ = robot_model_->getFeet2BPositions();   // 当前足部位置，相对于body的

    // 初始化右前腿的足部位置
    fr_init_pos_ = init_foot_pos_[0];   // 右前腿初始位置

    // 将所有足部位置相对于右前腿位置做平移
    for (auto &foot_pos: init_foot_pos_) {
        foot_pos.p -= fr_init_pos_.p;
        foot_pos.M = KDL::Rotation::RPY(0, 0, 0);
    }

    // 设置控制输入命令
    ctrl_comp_.control_inputs_.command = 0;
}


void StateFreeStand::run() {
    calc_body_target(invNormalize(ctrl_comp_.control_inputs_.lx, row_min_, row_max_),
                     invNormalize(ctrl_comp_.control_inputs_.ly, pitch_min_, pitch_max_),
                     invNormalize(ctrl_comp_.control_inputs_.rx, yaw_min_, yaw_max_),
                     invNormalize(ctrl_comp_.control_inputs_.ry, height_min_, height_max_));
}

void StateFreeStand::exit() {
    ctrl_comp_.control_inputs_.command = 0;
}

FSMStateName StateFreeStand::checkChange() {
    switch (ctrl_comp_.control_inputs_.command) {
        case 1:
            return FSMStateName::PASSIVE;
        case 2:
            return FSMStateName::FIXEDSTAND;
        default:
            return FSMStateName::FREESTAND;
    }
}

// 根据身体姿态计算目标关节角度
void StateFreeStand::calc_body_target(const float row, const float pitch,
                                      const float yaw, const float height) {
    KDL::Frame fr_2_body_pos;
    fr_2_body_pos.p = -fr_init_pos_.p;
    fr_2_body_pos.p.z(fr_2_body_pos.p.z() + height);
    fr_2_body_pos.M = KDL::Rotation::RPY(row, pitch, -yaw);

    const KDL::Frame body_2_fr_pos = fr_2_body_pos.Inverse();
    std::vector goal_pos(4, KDL::Frame::Identity());
    for (int i = 0; i < 4; i++) {
        goal_pos[i] = body_2_fr_pos * init_foot_pos_[i];
    }
    target_joint_pos_ = robot_model_->getQ(goal_pos); // 计算目标关节角度

    for (int i = 0; i < 4; i++) {
        ctrl_comp_.joint_position_command_interface_[i * 3].get().set_value(target_joint_pos_[i](0));       //位置控制接口
        ctrl_comp_.joint_position_command_interface_[i * 3 + 1].get().set_value(target_joint_pos_[i](1));  
        ctrl_comp_.joint_position_command_interface_[i * 3 + 2].get().set_value(target_joint_pos_[i](2));
    }
}