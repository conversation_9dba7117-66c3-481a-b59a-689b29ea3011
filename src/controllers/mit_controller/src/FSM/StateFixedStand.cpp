//
// Created by <PERSON><PERSON> on 24-9-10.
//

#include "mit_controller/FSM/StateFixedStand.h"

#include <cmath>

StateFixedStand::StateFixedStand(CtrlComponent &ctrlComp): FSMState(
    FSMStateName::FIXEDSTAND, "fixed stand", ctrlComp) {
    duration_ = ctrl_comp_.frequency_ * 1.2;
}

void StateFixedStand::enter() {
    for (int i = 0; i < 12; i++) {
        start_pos_[i] = ctrl_comp_.joint_position_state_interface_[i].get().get_optional().value();
    }
    ctrl_comp_.control_inputs_.command = 0;
}

void StateFixedStand::run() {
    percent_ += 1 / duration_;
    phase = std::tanh(percent_);

    for (int i = 0; i < 12; i++) {
        // 设置关节位置
        bool success = ctrl_comp_.joint_position_command_interface_[i].get().set_value(
            phase * target_pos_[i] + (1 - phase) * start_pos_[i]);
        if (!success) {
            std::cerr << "Failed to set joint position command for joint " << i << std::endl;
        }

        // 设置关节速度
        success = ctrl_comp_.joint_velocity_command_interface_[i].get().set_value(0.0);
        if (!success) {
            std::cerr << "Failed to set joint velocity command for joint " << i << std::endl;
        }

        // 设置关节扭矩
        success = ctrl_comp_.joint_torque_command_interface_[i].get().set_value(0.0);
        if (!success) {
            std::cerr << "Failed to set joint torque command for joint " << i << std::endl;
        }

        // 设置关节的比例增益
        success = ctrl_comp_.joint_kp_command_interface_[i].get().set_value(
            phase * 60.0 + (1 - phase) * 20.0);
        if (!success) {
            std::cerr << "Failed to set joint kp command for joint " << i << std::endl;
        }

        // 设置关节的微分增益
        success = ctrl_comp_.joint_kd_command_interface_[i].get().set_value(3.5);
        if (!success) {
            std::cerr << "Failed to set joint kd command for joint " << i << std::endl;
        }
    }
}

void StateFixedStand::exit() {
    percent_ = 0;
}

FSMStateName StateFixedStand::checkChange() {
    if (percent_ < 1.5) {
        return FSMStateName::FIXEDSTAND;
    }
    switch (ctrl_comp_.control_inputs_.command) {
        case 1:
            return FSMStateName::PASSIVE;
        case 2:
            return FSMStateName::FIXEDDOWN;
        case 3:
            return FSMStateName::FREESTAND;
        case 4:
            return FSMStateName::TROTTING;
        case 5:
            return FSMStateName::SWINGTEST;
        case 6:
            return FSMStateName::BALANCETEST;
        default:
            return FSMStateName::FIXEDSTAND;
    }
}