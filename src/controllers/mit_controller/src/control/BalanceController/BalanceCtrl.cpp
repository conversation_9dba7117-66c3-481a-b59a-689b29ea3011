//
// Created by tlab-uav on 24-9-16.
//

#include "mit_controller/control/BalanceController/BalanceCtrl.h"

#include <mit_controller/common/mathTools.h>
#include <mit_controller/robot/QuadrupedRobot.h>

#include "quadProgpp/QuadProg++.hh"

BalanceCtrl::BalanceCtrl(const std::shared_ptr<QuadrupedRobot> &robot) {
    mass_ = robot->mass_; // 机器人质量

    alpha_ = 0.001;     
    beta_ = 0.1;
    g_ << 0, 0, -9.81;

    friction_ratio_ = 0.4; // 摩擦系数mu
    /*
    对于每只接触足，约束条件为：
    [ 1   0   mu]       [0]
    [-1   0   mu] [fx]  [0]
    [ 0   1   mu]*[fy]>=[0]    
    [ 0  -1   mu] [fz]  [0]
    [ 0   0   1 ]       [0] 
    */
    friction_mat_ << 1, 0, friction_ratio_, -1, 0, friction_ratio_, 0, 1, friction_ratio_, 0, -1,
            friction_ratio_, 0, 0, 1;   // 摩擦系数矩阵

    pcb_ = Vec3(0.0, 0.0, 0.0);
    Ib_ = Vec3(0.0792, 0.2085, 0.2265).asDiagonal(); // 机器人惯性矩阵,数值哪来的？

    Vec6 s;
    Vec12 w, u;
    w << 10, 10, 4, 10, 10, 4, 10, 10, 4, 10, 10, 4; 
    u << 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3;
    s << 20, 20, 50, 450, 450, 450;

    /*
    QP优化问题：
    F* = min(AF-Bd)^T*S*(AF-Bd) + alpha*F^T*W*F + beta*(F-F_prev)^T*U*(F-F_prev)
    st. 
        CE_*F = ce_
        CI_*F >= ci_
    */

    S_ = s.asDiagonal();    // 权重矩阵
    W_ = w.asDiagonal();    
    U_ = u.asDiagonal();

    F_prev_.setZero();
}

// 计算接触力
Vec34 BalanceCtrl::calF(const Vec3 &ddPcd, const Vec3 &dWbd, const RotMat &rot_matrix,
                        const Vec34 &feet_pos_2_body, const VecInt4 &contact) {

    /*
    QP优化问题：
    F* = min(AF-Bd)^T*S*(AF-Bd) + alpha*F^T*W*F + beta*(F-F_prev)^T*U*(F-F_prev)
       = min 1/2 f^T*G*f + g0^T*f
    st. 
        CE_*F = ce_
        CI_*F >= ci_
    */
    calMatrixA(feet_pos_2_body, rot_matrix); // 计算矩阵A
    calVectorBd(ddPcd, dWbd, rot_matrix);   // 计算向量bd
    calConstraints(contact);    // 计算约束矩阵CI_,CE_,ci_,ce_

    G_ = A_.transpose() * S_ * A_ + alpha_ * W_ + beta_ * U_;
    g0T_ = -bd_.transpose() * S_ * A_ - beta_ * F_prev_.transpose() * U_;

    solveQP();  //  求解二次规划问题

    F_prev_ = F_;
    return vec12ToVec34(F_);
}

void BalanceCtrl::calMatrixA(const Vec34 &feet_pos_2_body, const RotMat &rotM) {
    /*
        A_ = [    I3       I3       I3         I3   ]
             [(p1-pc)x  (p2-pc)x  (p3-pc)x  (p4-pc)x]
    */
    for (int i = 0; i < 4; ++i) {
        A_.block(0, 3 * i, 3, 3) = I3;
        A_.block(3, 3 * i, 3, 3) = skew(Vec3(feet_pos_2_body.col(i)) - rotM * pcb_);
    }
}

void BalanceCtrl::calVectorBd(const Vec3 &ddPcd, const Vec3 &dWbd, const RotMat &rotM) {
    /*
        bd_ = [mass*(ddPc_d-g_)]
              [    IG*dWb_d    ]
    */
    bd_.head(3) = mass_ * (ddPcd - g_);
    bd_.tail(3) = rotM * Ib_ * rotM.transpose() * dWbd;
}

void BalanceCtrl::calConstraints(const VecInt4 &contact) {
    int contactLegNum = 0;
    for (int i(0); i < 4; ++i) {
        if (contact[i] == 1) {
            contactLegNum += 1;
        }
    }

     // 触地足摩擦约束， 每个触地足有5个约束， 12为接触力的维度
    CI_.resize(5 * contactLegNum, 12);
    ci0_.resize(5 * contactLegNum);
    // 非接触足支撑约束 [fx fy fz] = 0， 每个非接触足有3个约束
    CE_.resize(3 * (4 - contactLegNum), 12);
    ce0_.resize(3 * (4 - contactLegNum));

    CI_.setZero();
    ci0_.setZero(); // 右边的0向量
    CE_.setZero();
    ce0_.setZero(); // 右边的0向量

    int ceID = 0;
    int ciID = 0;
    for (int i(0); i < 4; ++i) {
        if (contact[i] == 1) { // 接触足
            CI_.block(5 * ciID, 3 * i, 5, 3) = friction_mat_;
            ++ciID;
        } else {    // 非接触足
            CE_.block(3 * ceID, 3 * i, 3, 3) = I3;
            ++ceID;
        }
    }
}

void BalanceCtrl::solveQP() {
    const long n = F_.size();       // 决策变量维度
    const long m = ce0_.size();     // 等式约束维度
    const long p = ci0_.size();     // 不等式约束维度

    /*
        solve_quadprog的求解二次规划问题：
        min 1/2 * x^T * G * x + g0^T * x
        s.t. CE^T * x  = ce0
             CI^T * x  >= ci0
    */

    quadprogpp::Matrix<double> G, CE, CI;
    quadprogpp::Vector<double> g0, ce0, ci0, x;

    G.resize(n, n);
    CE.resize(n, m);
    CI.resize(n, p);
    g0.resize(n);
    ce0.resize(m);
    ci0.resize(p);
    x.resize(n); // qp求解得到的决策变量

    for (int i = 0; i < n; ++i) {
        for (int j = 0; j < n; ++j) {
            G[i][j] = G_(i, j);
        }
    }

    for (int i = 0; i < n; ++i) {
        for (int j = 0; j < m; ++j) {
            CE[i][j] = CE_.transpose()(i, j); // 不加负号？
        }
    }

    for (int i = 0; i < n; ++i) {
        for (int j = 0; j < p; ++j) {
            CI[i][j] = CI_.transpose()(i, j);
        }
    }

    for (int i = 0; i < n; ++i) {
        g0[i] = g0T_[i];
    }

    for (int i = 0; i < m; ++i) {
        ce0[i] = ce0_[i];
    }

    for (int i = 0; i < p; ++i) {
        ci0[i] = ci0_[i];
    }

    solve_quadprog(G, g0, CE, ce0, CI, ci0, x);

    for (int i = 0; i < n; ++i) {
        F_[i] = x[i];
    }
}