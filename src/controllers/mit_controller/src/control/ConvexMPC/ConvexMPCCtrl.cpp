#include "mit_controller/control/ConvexMPC/ConvexMPCCtrl.h"

#include <mit_controller/common/mathTools.h>
#include <mit_controller/robot/QuadrupedRobot.h>
#include <unsupported/Eigen/MatrixFunctions>

#include "quadProgpp/QuadProg++.hh"

ConvexMPCCtrl::ConvexMPCCtrl(const std::shared_ptr<QuadrupedRobot>& robot)
{
  mass_ = robot->mass_;  // 机器人质量

  alpha_ = 1;
  beta_ = 1e-8;
  g_ << 0, 0, -9.81;

  friction_ratio_ = 0.4;  // 摩擦系数mu
  /*
  对于每只接触足，约束条件为：
  [ 1   0   mu]       [0]
  [-1   0   mu] [fx]  [0]
  [ 0   1   mu]*[fy]>=[0]
  [ 0  -1   mu] [fz]  [0]
  [ 0   0   1 ]       [0]
  */
  friction_mat_ << 1, 0, friction_ratio_, -1, 0, friction_ratio_, 0, 1, friction_ratio_, 0, -1, friction_ratio_, 0, 0,
      1;  // 摩擦系数矩阵

  pcb_ = Vec3(0.0, 0.0, 0.0);
  Ib_ = Vec3(0.0792, 0.2085, 0.2265).asDiagonal();  // 机器人惯性矩阵,数值哪来的？

  Qi_ << 0.25, 0.25, 10.0, 2.0, 2.0, 50.0, 0.1, 0.1, 0.3, 0.2, 0.2, 0.1, 0.0;
  Ri_ << 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3;
  /*
  MPC 优化问题：
  F* = min sum_(i=0)^{k-1} ||x_{i+1}-x_{i+1,ref}||_{Qi} +||u_i||_{R_i}
  st.
      x_{i+1} = A*x_i + B*u_i 
      C*x_i >= c
      D*x_i = d
  */

  Qi_ = Qi_*alpha_;  // 权重矩阵
  Ri_ = Ri_*beta_;

  F_prev_.setZero();
}

// 计算接触力
Vec34 ConvexMPCCtrl::calF(const Vec13 &x0_, const Vec3& dPcd, const Vec3& Wbd, const RotMat& R_yaw, const RotMat &rot_matrix, const Vec34& feet_pos_2_body,
                          const VecInt4& contact, const double &dt_)
{
  /*
  QP优化问题：
  U* = min 1/2 U^T*H*U + f^T*U
  st.
      CE_*U >= ce_
      CI_*U = ci_

    H = 2(Bqp_^T*Q*Bqp_ + R_))
    f = 2(Aqp_*x0-X_ref)^T*Q*Bqp_
  */
  // std::cout << "x0_: " << x0_ << std::endl;
  // std::cout << "vel_target_: " << dPcd << std::endl;
  calMatrixAcBc(feet_pos_2_body, R_yaw, rot_matrix);  // 计算矩阵Ac,Bc
  // std::cout << "Ac_: " << Ac_ << std::endl;
  Continuous2Discrete(dt_);                    // 计算离散化矩阵
  calAqpBqp();
  // std::cout << "Aqp_: " << Aqp_ << std::endl;
  // std::cout << "Bqp_: " << Bqp_ << std::endl;
  MatX Q_;
  MatX R_;
  Q_.resize(13 * Horizon_, 13 * Horizon_); 
  Q_.setZero();  
  R_.resize(12 * Horizon_, 12 * Horizon_);
  R_.setZero();
  Q_.diagonal() = Qi_.replicate(Horizon_,1); // A.replicate(p, q) 会将 A 复制 p 次行和 q 次列，生成一个新的大矩阵,赋值给Q的对角阵
  R_.diagonal() = Ri_.replicate(Horizon_,1);
  // std::cout << "Q_: " << Q_ << std::endl;
  // 期望的参考状态为
  // x_0 = [0;0;x0_(2); pcd; 0;0;dyaw; dPcd]
  // x_i = [0;0;x0_(2)+dyaw*i*dt_; pcd+ddPcd*i*dt_; 0;0;ddyaw*i*dt_; dPcd+ddPcd*i*dt_]
  VecX X_ref;
  X_ref.resize(13 * Horizon_);
  X_ref.setZero();
  for (int16_t i = 0; i < Horizon_; ++i)
  {
    X_ref.segment(i*13,13) << 0,0,x0_(2)+Wbd(2)*dt_*(i+1),           // 期望rpy
        x0_(3)+dPcd(0)*dt_*(i+1),x0_(4)+dPcd(1)*dt_*(i+1),x0_(5), //  期望Pcd
        Wbd(0),Wbd(1),Wbd(2),                           // 期望Wbd 角速度
        dPcd(0),dPcd(1),0,                              // 期望dPcd
        -9.81;
  }
  // std::cout << "X_ref1: " << X_ref.segment(0,13) << std::endl;
  // std::cout << "X_ref2: " << X_ref.segment(13,13) << std::endl;

  calConstraints(contact);  // 计算约束矩阵CI_,CE_,ci_,ce_
  // std::cout << "CI_: " << CI_ << std::endl;
  // x0_,X_ref怎么给定？
  H_qpoases.resize(12 * Horizon_, 12 * Horizon_);
  H_qpoases.setZero();
  fT_qpoases.resize(12 * Horizon_);
  fT_qpoases.setZero();
  H_qpoases = 2*(Bqp_.transpose()*Q_*Bqp_ + R_);
  fT_qpoases = 2*(Aqp_*x0_ - X_ref).transpose()*Q_*Bqp_;
  // std::cout << "H_qpoases: " << H_qpoases << std::endl;
  
  solveQP();  //  求解二次规划问题

  // std::cout << "F_: " << F_ << std::endl;
  F_prev_ = F_;
  return vec12ToVec34(F_);
}

void ConvexMPCCtrl::calMatrixAcBc(const Vec34& feet_pos_2_body, const RotMat& R_yaw, const RotMat &rot_matrix)
{
  /*
       [03 03 Rz(yaw) 03   0]
       [03 03   03    I3   0]   // MIT代码这里还加了补偿
  Ac = [03 03   03    03   0]
       [03 03   03    03   [0;0;1]]
       [0. 0.   0.    0.   0]  // 重力部分
  */
  Ac_.setZero();
  Ac_.block(0, 6, 3, 3) = R_yaw;  // 为什么代码里是R_yaw.transpose()?
  Ac_.block(3, 9, 3, 3) = I3;
  Ac_(11, 12) = 1.0;

  /*
       全局坐标系下的转动惯量Ibg = Rz*Ib_*Rz^T, 逆矩阵为 Ibg_inv =  (Rz*Ib_*Rz^T)^{-1}
        [          03              03                      03              03             ]
        [          03              03                      03              03             ]
   Bc = [ Ibg_inv*(p1-pc)x     Ibg_inv*(p2-pc)x    Ibg_inv*(p3-pc)x    Ibg_inv*(p4-pc)x   ]
        [        I3/mass_          I3/mass_              I3/mass_          I3/mass_       ]
        [          0.              0.                      0.              0.             ] // 重力部分
   */
  Mat3 Ibg_inv = (R_yaw * Ib_ * R_yaw.transpose()).inverse();
  Bc_.setZero();
  for (int16_t i = 0; i < 4; ++i)
  {
    Bc_.block(6, 3 * i, 3, 3) = Ibg_inv * skew(Vec3(feet_pos_2_body.col(i)) - rot_matrix * pcb_);
    Bc_.block(9, 3 * i, 3, 3) = I3 / mass_;
  }
}

void ConvexMPCCtrl::Continuous2Discrete(const double &dt_)
{
  // 精确离散化
  // std::cout << "dt_: " << dt_ << std::endl;
  Eigen::Matrix<double,25,25> ABc,expmm;
  ABc.setZero();
  ABc.block(0,0,13,13) = Ac_;
  ABc.block(0,13,13,12) = Bc_;
  ABc = dt_*ABc;
  expmm = ABc.exp();
  Ad_ = expmm.block(0,0,13,13);
  Bd_ = expmm.block(0,13,13,12);
}

void ConvexMPCCtrl::calAqpBqp()
{
  if (Horizon_ > 19)
  {
    throw std::runtime_error("horizon is too long!");
  }

  Eigen::Matrix<double,13, 13> powerMats[20];
  powerMats[0].setIdentity();
  for (int i = 1; i < Horizon_ + 1; i++)
  {
    powerMats[i] = Ad_ * powerMats[i - 1];
  }

  Aqp_.resize(13 * Horizon_, 13); // 动态调整行数
  Aqp_.setZero();                 // 初始化为零，确保未赋值部分不受影响
  Bqp_.resize(13 * Horizon_, 12 * Horizon_);
  Bqp_.setZero();

  for (int16_t r = 0; r < Horizon_; r++)
  {
    Aqp_.block(13 * r, 0, 13, 13) = powerMats[r + 1];  // Ad_.pow(r+1);
    for (int16_t c = 0; c < Horizon_; c++)
    {
      if (r >= c)
      {
        int16_t a_num = r - c;
        Bqp_.block(13 * r, 12 * c, 13, 12) = powerMats[a_num] /*Ad_.pow(a_num)*/ * Bd_;
      }
    }
  }
}


void ConvexMPCCtrl::calConstraints(const VecInt4& contact)
{
  int contactLegNum = 0;
  for (int i(0); i < 4; ++i)
  {
    if (contact[i] == 1)
    {
      contactLegNum += 1;
    }
  }

  // 触地足摩擦约束， 每个触地足有5个约束， 12为接触力的维度
  Eigen::MatrixXd CE, CI; // QP约束矩阵
  Eigen::VectorXd ce0, ci0; // QP约束向量
  CI.resize(5 * contactLegNum, 12);
  ci0.resize(5 * contactLegNum);
  // 非接触足支撑约束 [fx fy fz] = 0， 每个非接触足有3个约束
  CE.resize(3 * (4 - contactLegNum), 12);
  ce0.resize(3 * (4 - contactLegNum));

  CI.setZero();
  ci0.setZero();  // 右边的0向量
  CE.setZero();
  ce0.setZero();  // 右边的0向量

  int ceID = 0;
  int ciID = 0;
  for (int i(0); i < 4; ++i)
  {
    if (contact[i] == 1)
    {  // 接触足,不等式约束
      CI.block(5 * ciID, 3 * i, 5, 3) = friction_mat_;
      ++ciID;
    }
    else
    {  // 非接触足，等式约束
      CE.block(3 * ceID, 3 * i, 3, 3) = I3;
      ++ceID;
    }
  }

  CI_.resize(5 * contactLegNum* Horizon_, 12 * Horizon_);  // 维数应该为 p x n
  CI_.setZero();
  ci0_.resize(5 * contactLegNum * Horizon_);
  ci0_.setZero();
  CE_.resize(3 * (4 - contactLegNum)* Horizon_, 12 * Horizon_); // 维数应该为 m x n
  CE_.setZero();
  ce0_.resize(3 * (4 - contactLegNum) * Horizon_);
  ce0_.setZero();

  for (int i = 0; i < Horizon_; ++i)
  {
    CI_.block(5 * contactLegNum * i, 12 * i, 5 * contactLegNum, 12) = CI;
    ci0_.block(5 * contactLegNum * i, 0, 5 * contactLegNum, 1) = ci0;
    CE_.block(3 * (4 - contactLegNum) * i, 12 * i, 3 * (4 - contactLegNum), 12) = CE;
    ce0_.block(3 * (4 - contactLegNum) * i, 0, 3 * (4 - contactLegNum), 1) = ce0;
  }
}

void ConvexMPCCtrl::solveQP()
{
  const long n = F_.size()*Horizon_;    // 决策变量维度
  const long m = ce0_.size();  // 等式约束维度
  const long p = ci0_.size();  // 不等式约束维度

  /*
      solve_quadprog的求解二次规划问题：
      min 1/2 * x^T * G * x + g0^T * x
      s.t. CE^T * x  = ce0
           CI^T * x  >= ci0
  */

  quadprogpp::Matrix<double> G, CE, CI;
  quadprogpp::Vector<double> g0, ce0, ci0, x;

  G.resize(n, n);
  CE.resize(n, m); // 等式约束的尺寸应为 n x m
  CI.resize(n, p); // 不等式约束的尺寸应为 n x p
  g0.resize(n);
  ce0.resize(m);
  ci0.resize(p);
  x.resize(n);  // qp求解得到的决策变量

  for (int i = 0; i < n; ++i)
  {
    for (int j = 0; j < n; ++j)
    {
      G[i][j] = H_qpoases(i, j);
    }
  }
  // Eigen::SelfAdjointEigenSolver<Eigen::MatrixXd> es(H_qpoases);
  // std::cout << "Eigenvalues of H_qpoases:\n" << es.eigenvalues() << std::endl;
  
  for (int i = 0; i < n; ++i)
  {
    for (int j = 0; j < m; ++j)
    {
      CE[i][j] = CE_.transpose()(i, j);
    }
  }

  for (int i = 0; i < n; ++i)
  {
    for (int j = 0; j < p; ++j)
    {
      CI[i][j] = CI_.transpose()(i, j);
    }
  }

  for (int i = 0; i < n; ++i)
  {
    g0[i] = fT_qpoases[i];
  }

  for (int i = 0; i < m; ++i)
  {
    ce0[i] = ce0_[i];
  }

  for (int i = 0; i < p; ++i)
  {
    ci0[i] = ci0_[i];
  }

  solve_quadprog(G, g0, CE, ce0, CI, ci0, x);

  // 取第一个解向量
  for (int i = 0; i < F_.size(); ++i)
  {
    F_[i] = x[i];
  }
}