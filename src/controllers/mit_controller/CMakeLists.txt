cmake_minimum_required(VERSION 3.8)
project(mit_controller)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

set(CMAKE_BUILD_TYPE Release)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# find dependencies
set(CONTROLLER_INCLUDE_DEPENDS
  control_input_msgs
  std_msgs
  controller_interface
  hardware_interface
  pluginlib
  rclcpp
  rcpputils
  # rclcpp_lifecycle
  realtime_tools
  # std_srvs
  kdl_parser
)

find_package(ament_cmake REQUIRED)
find_package(generate_parameter_library REQUIRED)
foreach(Dependency IN ITEMS ${CONTROLLER_INCLUDE_DEPENDS})
  find_package(${Dependency} REQUIRED)
endforeach()

# # Add MITController library related compile commands
# generate_parameter_library(MITController_parameters
#   src/MITController.yaml
#   include/mit_controller/validate_MITController_parameters.hpp
# )
# add_library(
#   MITController
#   SHARED
#   src/MITController.cpp
# )
add_library(${PROJECT_NAME} SHARED

        src/MITController.cpp

        src/FSM/StatePassive.cpp
        src/FSM/StateFixedDown.cpp
        src/FSM/StateFixedStand.cpp
        src/FSM/StateSwingTest.cpp
        src/FSM/StateFreeStand.cpp
        src/FSM/StateBalanceTest.cpp
        src/FSM/StateTrotting.cpp

        src/robot/QuadrupedRobot.cpp
        src/robot/RobotLeg.cpp

        src/control/Estimator/Estimator.cpp
        src/control/Estimator/LowPassFilter.cpp
        src/control/BalanceController/BalanceCtrl.cpp
        src/control/ConvexMPC/ConvexMPCCtrl.cpp

        src/quadProgpp/Array.cc
        src/quadProgpp/QuadProg++.cc

        src/gait/WaveGenerator.cpp
        src/gait/FeetEndCalc.cpp
        src/gait/GaitGenerator.cpp

        src/safety/SafetyChecker.cpp
        src/wbc/WbcBase.cpp
        src/wbc/WeightedWbc.cpp

)

target_include_directories(${PROJECT_NAME} PUBLIC
  "$<BUILD_INTERFACE:${PROJECT_SOURCE_DIR}/include>"
  "$<INSTALL_INTERFACE:include/${PROJECT_NAME}>"
  PRIVATE
  src)
# target_link_libraries(MITController MITController_parameters)
ament_target_dependencies(${PROJECT_NAME} PUBLIC ${CONTROLLER_INCLUDE_DEPENDS})
target_compile_definitions(${PROJECT_NAME}  PRIVATE "MITCONTROLLER_BUILDING_DLL")

pluginlib_export_plugin_description_file(
  controller_interface mit_controller.xml)

install(
  TARGETS ${PROJECT_NAME}
  EXPORT export_${PROJECT_NAME}
  RUNTIME DESTINATION bin
  ARCHIVE DESTINATION lib/${PROJECT_NAME}
  LIBRARY DESTINATION lib/${PROJECT_NAME}
)

install(
  DIRECTORY include/
  DESTINATION include/${PROJECT_NAME}
)

install(
        DIRECTORY launch
        DESTINATION share/${PROJECT_NAME}/
)

ament_export_dependencies(${CONTROLLER_INCLUDE_DEPENDS})
ament_export_targets(export_${PROJECT_NAME} HAS_LIBRARY_TARGET)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # comment the line when a copyright and license is added to all source files
  set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # comment the line when this package is in a git repo and when
  # a copyright and license is added to all source files
endif()

ament_package()