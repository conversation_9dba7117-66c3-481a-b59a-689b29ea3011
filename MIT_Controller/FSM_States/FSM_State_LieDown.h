// FSM_State_LieDown.h
#ifndef FSM_STATE_LIEDOWN_H
#define FSM_STATE_LIEDOWN_H

#include "FSM_State.h"

/**
 *
 */
template <typename T>
class FSM_State_LieDown : public FSM_State<T> {
 public:
  FSM_State_LieDown(ControlFSMData<T>* _controlFSMData);

  // Behavior to be carried out when entering a state
  void onEnter();

  // Run the normal behavior for the state
  void run();

  // Checks for any transition triggers
  FSM_StateName checkTransition();

  // Manages state specific transitions
  TransitionData<T> transition();

  // Behavior to be carried out when exiting a state
  void onExit();

  TransitionData<T> testTransition();

 private:
  // Keep track of the control iterations
  unsigned long long curr_iter;
  // JPos
  Vec3<T> fold_jpos[4];
  Vec3<T> initial_jpos[4];
  Vec3<T> zero_vec3;

  const int lie_down_ramp_iter = 500;
  
};

#endif  // FSM_STATE_LIEDOWN_H