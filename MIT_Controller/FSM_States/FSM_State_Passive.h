#ifndef FSM_STATE_PASSIVE_H
#define FSM_STATE_PASSIVE_H

#include "FSM_State.h"

/**
 *
 */
template <typename T>
class FSM_State_Passive : public FSM_State<T> {
 public:
  FSM_State_Passive(ControlFSMData<T>* _controlFSMData);

  // Behavior to be carried out when entering a state
  void onEnter();

  // Run the normal behavior for the state
  void run();

  // Checks for any transition triggers
  FSM_StateName checkTransition();

  // Manages state specific transitions
  TransitionData<T> transition();

  // Behavior to be carried out when exiting a state
  void onExit();

  TransitionData<T> testTransition();

 private:
  // Keep track of the control iterations
  int iter = 0;

  float base_motor[12] = {
                          0.078610444444445,	-0.956823083333333,	3.00007161111111,
                          0.366259855555556,	1.01167308333333,	-2.84656761111111,
                          0.361834755555556,	-1.39215308333333,	2.76500951111111,
                          -0.008517555555556,	0.968643083333333,	-2.76523841111111
                          };  

//   float in_base_motor[12] = {0.323001155555556,	1.34393308333333,	-2.69275921111111,
//                              0.086239444444445,	-0.914173083333333,	2.77599581111111,
//                             -0.056582555555556,	1.43732308333333,	-2.57213861111111,
//                             -0.068615444444444,	-1.36530308333333,	2.69878641111111
//                             };
  const vector<int> direction_motor{-1, -1, -1,
                                    -1, 1, 1,
                                     1, -1, -1,
                                     1, 1, 1};
};

#endif  // FSM_STATE_PASSIVE_H
