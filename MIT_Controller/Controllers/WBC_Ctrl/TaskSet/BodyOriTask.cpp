#include "BodyOriTask.hpp"
// (Rx, Ry, Rz)

//#include <Configuration.h>
#include <Dynamics/FloatingBaseModel.h>
#include <Dynamics/Quadruped.h>
#include <Math/orientation_tools.h>
#include <Utilities/Utilities_print.h>


template <typename T>
BodyOriTask<T>::BodyOriTask(const FloatingBaseModel<T>* robot)
    : Task<T>(3), _robot_sys(robot) {
  TK::Jt_ = DMat<T>::Zero(TK::dim_task_, cheetah::dim_config);
  TK::Jt_.block(0, 0, 3, 3).setIdentity();
  TK::JtDotQdot_ = DVec<T>::Zero(TK::dim_task_);

  _Kp_kin = DVec<T>::Constant(TK::dim_task_, 1.);
  _Kp = DVec<T>::Constant(TK::dim_task_, 100.);//50.);//
  _Kd = DVec<T>::Constant(TK::dim_task_, 10.);//1.);//
}

template <typename T>
BodyOriTask<T>::~BodyOriTask() {}

template <typename T>
bool BodyOriTask<T>::_UpdateCommand(const void* pos_des, const DVec<T>& vel_des,
                                    const DVec<T>& acc_des) {
  Quat<T>* ori_cmd = (Quat<T>*)pos_des;
  Quat<T> link_ori = (_robot_sys->_state.bodyOrientation);

  Quat<T> link_ori_inv;
  link_ori_inv[0] = link_ori[0];
  link_ori_inv[1] = -link_ori[1];
  link_ori_inv[2] = -link_ori[2];
  link_ori_inv[3] = -link_ori[3];
  // link_ori_inv /= link_ori.norm();

  // Explicit because operational space is in global frame
  Quat<T> ori_err = ori::quatProduct(*ori_cmd, link_ori_inv);
  if (ori_err[0] < 0.) {
    ori_err *= (-1.);
  }
  Vec3<T> ori_err_so3;
  ori::quaternionToso3(ori_err, ori_err_so3);
  SVec<T> curr_vel = _robot_sys->_state.bodyVelocity;

  // Configuration space: Local
  // Operational Space: Global
  Mat3<T> Rot = ori::quaternionToRotationMatrix(link_ori);
  Vec3<T> vel_err = Rot.transpose()*(TK::vel_des_ - curr_vel.head(3));
    for(int m=0;m<3;m++)
    {
        if(fabs(vel_err[m])>1000)
        {
            std::cout<<"body Ori Task TK::vel_des_  "<<TK::vel_des_[m]<<"\toverflow, edit to  ";
            vel_err[m]= 0 ;
            std::cout<<vel_err[m]<<std::endl;
        }
    }
  // Rx, Ry, Rz
  for (int i(0); i < 3; ++i) {
    TK::pos_err_[i] = _Kp_kin[i] * ori_err_so3[i];
    TK::vel_des_[i] = vel_des[i];
    TK::acc_des_[i] = acc_des[i];

    TK::op_cmd_[i] = _Kp[i] * ori_err_so3[i] +
                     _Kd[i] * vel_err[i] + TK::acc_des_[i];
      if(fabs(TK::op_cmd_[i])>100)
      {
          std::cout<<"-------------------big problem------------------------------"<<std::endl;
          std::cout<<"bodyOriTask acc error"<<TK::op_cmd_[i]<<std::endl;
          std::cout<<"ori_err_so3[i]"<<ori_err_so3[i]<<std::endl;
          std::cout<<"vel_err[i]"<<vel_err[i]<<std::endl;
          std::cout<<"TK::acc_des_[i]"<<TK::acc_des_[i]<<std::endl;
      }
  }
   //printf("[Body Ori Task]\n");
   //pretty_print(TK::pos_err_, std::cout, "pos_err_");
   //pretty_print(*ori_cmd, std::cout, "des_ori");
   //pretty_print(link_ori, std::cout, "curr_ori");
   //pretty_print(ori_err, std::cout, "quat_err");

  // pretty_print(link_ori_inv, std::cout, "ori_inv");
  // pretty_print(ori_err, std::cout, "ori_err");
  // pretty_print(*ori_cmd, std::cout, "cmd");
  // pretty_print(acc_des, std::cout, "acc_des");
  // pretty_print(TK::Jt_, std::cout, "Jt");

  return true;
}

template <typename T>
bool BodyOriTask<T>::_UpdateTaskJacobian() {
  Quat<T> quat = _robot_sys->_state.bodyOrientation;
  Mat3<T> Rot = ori::quaternionToRotationMatrix(quat);
  TK::Jt_.block(0, 0, 3, 3) = Rot.transpose();
  //pretty_print(Rot, std::cout, "Rot mat");
  return true;
}

template <typename T>
bool BodyOriTask<T>::_UpdateTaskJDotQdot() {
  return true;
}

template class BodyOriTask<double>;
template class BodyOriTask<float>;
